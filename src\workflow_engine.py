#!/usr/bin/env python3
"""
Full BMAD Workflow Engine for Aetherforge

This module implements a comprehensive workflow engine that can parse and execute
BMAD (Behavior-driven Multi-Agent Development) workflow YAML definitions with
support for:

- Conditional steps and branching logic
- Optional tasks with graceful failure handling
- Dynamic agent assignment and load balancing
- Parallel and sequential execution
- Retry mechanisms and timeout handling
- Real-time monitoring and progress tracking
- Integration with pheromone communication system

Features:
- YAML workflow definition parsing with validation
- State machine-based execution engine
- Conditional logic (if/else, loops, switches)
- Dynamic agent selection based on capabilities
- Error handling and recovery mechanisms
- Comprehensive logging and monitoring
- Integration with Aetherforge orchestrator
"""

import os
import yaml
import json
import asyncio
import logging
import time
import uuid
import re
import copy
import traceback
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum, IntEnum
from collections import defaultdict, deque
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class WorkflowStatus(str, Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class StepStatus(str, Enum):
    """Individual step execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    OPTIONAL_FAILED = "optional_failed"
    RETRYING = "retrying"

class StepType(str, Enum):
    """Types of workflow steps"""
    TASK = "task"
    CONDITION = "condition"
    LOOP = "loop"
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    AGENT_ASSIGNMENT = "agent_assignment"
    PHEROMONE_DROP = "pheromone_drop"
    WAIT = "wait"
    CUSTOM = "custom"

class ConditionOperator(str, Enum):
    """Conditional operators for step conditions"""
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    LESS_THAN = "lt"
    GREATER_EQUAL = "ge"
    LESS_EQUAL = "le"
    CONTAINS = "contains"
    NOT_CONTAINS = "not_contains"
    REGEX_MATCH = "regex"
    EXISTS = "exists"
    NOT_EXISTS = "not_exists"
    IN_LIST = "in"
    NOT_IN_LIST = "not_in"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    IS_EMPTY = "is_empty"
    IS_NOT_EMPTY = "is_not_empty"
    AND = "and"
    OR = "or"
    NOT = "not"
    NOT_EXISTS = "not_exists"

class AgentSelectionStrategy(str, Enum):
    """Strategies for dynamic agent selection"""
    ROUND_ROBIN = "round_robin"
    LEAST_LOADED = "least_loaded"
    CAPABILITY_MATCH = "capability_match"
    RANDOM = "random"
    PRIORITY_BASED = "priority_based"
    CUSTOM = "custom"

# Legacy compatibility
PhaseStatus = StepStatus

@dataclass
class WorkflowVariable:
    """Workflow variable with type and value"""
    name: str
    value: Any
    type: str = "string"
    description: str = ""
    required: bool = False
    default: Any = None

@dataclass
class StepCondition:
    """Condition for conditional step execution"""
    variable: str
    operator: ConditionOperator
    value: Any
    description: str = ""

    # Complex condition support
    sub_conditions: List['StepCondition'] = field(default_factory=list)
    negate: bool = False

    def __post_init__(self):
        """Validate condition after initialization"""
        if self.operator in [ConditionOperator.AND, ConditionOperator.OR]:
            if not self.sub_conditions:
                raise ValueError(f"Operator {self.operator} requires sub_conditions")
        elif self.operator == ConditionOperator.NOT:
            if len(self.sub_conditions) != 1:
                raise ValueError("NOT operator requires exactly one sub_condition")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StepCondition':
        """Create condition from dictionary"""
        sub_conditions = []
        if 'sub_conditions' in data:
            sub_conditions = [cls.from_dict(sub) for sub in data['sub_conditions']]

        return cls(
            variable=data.get('variable', ''),
            operator=ConditionOperator(data.get('operator', 'eq')),
            value=data.get('value'),
            description=data.get('description', ''),
            sub_conditions=sub_conditions,
            negate=data.get('negate', False)
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert condition to dictionary"""
        result = {
            'variable': self.variable,
            'operator': self.operator.value,
            'value': self.value,
            'description': self.description,
            'negate': self.negate
        }

        if self.sub_conditions:
            result['sub_conditions'] = [sub.to_dict() for sub in self.sub_conditions]

        return result

@dataclass
class AgentRequirement:
    """Requirements for agent selection"""
    capabilities: List[str] = field(default_factory=list)
    min_performance: float = 0.0
    max_load: float = 1.0
    preferred_agents: List[str] = field(default_factory=list)
    excluded_agents: List[str] = field(default_factory=list)
    selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

@dataclass
class RetryConfig:
    """Retry configuration for steps"""
    max_attempts: int = 3
    delay_seconds: float = 1.0
    backoff_multiplier: float = 2.0
    max_delay_seconds: float = 60.0
    retry_on_errors: List[str] = field(default_factory=list)

@dataclass
class TimeoutConfig:
    """Timeout configuration for steps"""
    execution_timeout: float = 300.0  # 5 minutes default
    agent_response_timeout: float = 30.0
    total_timeout: float = 3600.0  # 1 hour default

@dataclass
class WorkflowStep:
    """Enhanced workflow step definition"""
    id: str
    name: str
    type: StepType
    description: str = ""

    # Execution configuration
    command: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    environment: Dict[str, str] = field(default_factory=dict)

    # Conditional execution
    condition: Optional[StepCondition] = None
    depends_on: List[str] = field(default_factory=list)

    # Agent assignment
    agent_requirements: Optional[AgentRequirement] = None
    assigned_agent: Optional[str] = None

    # Error handling
    optional: bool = False
    retry_config: Optional[RetryConfig] = None
    timeout_config: Optional[TimeoutConfig] = None
    on_failure: Optional[str] = None  # Step ID to execute on failure
    on_success: Optional[str] = None  # Step ID to execute on success

    # Parallel execution
    parallel_steps: List[str] = field(default_factory=list)
    wait_for_all: bool = True

    # Loop configuration
    loop_variable: Optional[str] = None
    loop_items: List[Any] = field(default_factory=list)
    loop_condition: Optional[StepCondition] = None
    max_iterations: int = 100

    # Output handling
    output_variables: Dict[str, str] = field(default_factory=dict)
    capture_output: bool = True

    # Metadata
    tags: Set[str] = field(default_factory=set)
    priority: int = 0
    estimated_duration: float = 60.0

@dataclass
class WorkflowPhase:
    """Legacy workflow phase - converted to WorkflowStep internally"""
    id: str
    name: str
    description: str
    agent: str
    creates: List[str] = field(default_factory=list)
    requires: List[str] = field(default_factory=list)
    optional_steps: List[str] = field(default_factory=list)
    duration_minutes: int = 30
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    outputs: List[str] = field(default_factory=list)
    notes: str = field(default="")
    condition: Optional[str] = None

    def to_workflow_step(self) -> WorkflowStep:
        """Convert legacy phase to modern workflow step"""
        return WorkflowStep(
            id=self.id,
            name=self.name,
            type=StepType.TASK,
            description=self.description,
            parameters={
                "creates": self.creates,
                "requires": self.requires,
                "optional_steps": self.optional_steps,
                "notes": self.notes
            },
            agent_requirements=AgentRequirement(
                preferred_agents=[self.agent] if self.agent else []
            ),
            estimated_duration=self.duration_minutes * 60,  # Convert to seconds
            condition=StepCondition(
                variable="legacy_condition",
                operator=ConditionOperator.EXISTS,
                value=self.condition
            ) if self.condition else None
        )

@dataclass
class WorkflowExecution:
    """Enhanced runtime execution state of a workflow"""
    id: str
    workflow_id: str
    status: WorkflowStatus
    started_at: float
    completed_at: Optional[float] = None

    # Execution context
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    step_results: Dict[str, Any] = field(default_factory=dict)
    step_status: Dict[str, StepStatus] = field(default_factory=dict)
    step_attempts: Dict[str, int] = field(default_factory=dict)

    # Agent assignments
    agent_assignments: Dict[str, str] = field(default_factory=dict)  # step_id -> agent_id
    agent_loads: Dict[str, float] = field(default_factory=dict)

    # Progress tracking
    total_steps: int = 0
    completed_steps: int = 0
    failed_steps: int = 0
    skipped_steps: int = 0

    # Error information
    last_error: Optional[str] = None
    error_details: Dict[str, Any] = field(default_factory=dict)

    # Performance metrics
    execution_time: float = 0.0
    agent_response_times: Dict[str, float] = field(default_factory=dict)

    # Legacy compatibility
    project_id: Optional[str] = None
    current_phase_index: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    progress: float = 0.0
    phase_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    agent_team: Optional[Dict[str, Any]] = None
    pheromone_bus: Optional[Dict[str, Any]] = None

@dataclass
class WorkflowDefinition:
    """Enhanced workflow definition parsed from YAML"""
    id: str
    name: str
    version: str
    description: str = ""

    # Workflow metadata
    author: str = ""
    created_at: str = ""
    tags: Set[str] = field(default_factory=set)

    # Execution configuration
    variables: Dict[str, WorkflowVariable] = field(default_factory=dict)
    steps: Dict[str, WorkflowStep] = field(default_factory=dict)
    step_order: List[str] = field(default_factory=list)

    # Global configuration
    global_timeout: float = 3600.0  # 1 hour
    global_retry_config: Optional[RetryConfig] = None
    parallel_execution: bool = False
    max_concurrent_steps: int = 5

    # Agent configuration
    required_capabilities: List[str] = field(default_factory=list)
    preferred_agents: List[str] = field(default_factory=list)
    agent_selection_strategy: AgentSelectionStrategy = AgentSelectionStrategy.CAPABILITY_MATCH

    # Integration settings
    pheromone_integration: bool = True
    progress_reporting: bool = True
    detailed_logging: bool = True

    # Legacy compatibility
    type: str = "bmad"
    project_types: List[str] = field(default_factory=list)
    phases: List[WorkflowPhase] = field(default_factory=list)
    total_duration_minutes: int = 0
    flow_diagram: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

# Exception Classes
class WorkflowValidationError(Exception):
    """Raised when workflow definition validation fails"""
    def __init__(self, message: str, errors: List[str] = None):
        super().__init__(message)
        self.errors = errors or []

class WorkflowExecutionError(Exception):
    """Raised when workflow execution fails"""
    def __init__(self, message: str, step_id: str = None, error_code: str = None):
        super().__init__(message)
        self.step_id = step_id
        self.error_code = error_code

class WorkflowTimeoutError(WorkflowExecutionError):
    """Raised when workflow execution times out"""
    pass

class AgentAssignmentError(WorkflowExecutionError):
    """Raised when agent assignment fails"""
    pass

class WorkflowYAMLParser:
    """Enhanced YAML parser for BMAD workflow definitions"""

    def __init__(self):
        self.validation_errors = []

    def parse_workflow_file(self, file_path: Union[str, Path]) -> WorkflowDefinition:
        """Parse workflow definition from YAML file"""
        file_path = Path(file_path)

        if not file_path.exists():
            raise WorkflowValidationError(f"Workflow file not found: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml_content = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise WorkflowValidationError(f"Invalid YAML syntax: {e}")
        except Exception as e:
            raise WorkflowValidationError(f"Error reading workflow file: {e}")

        return self.parse_workflow_dict(yaml_content)

    def parse_workflow_dict(self, yaml_data: Dict[str, Any]) -> WorkflowDefinition:
        """Parse workflow definition from dictionary"""
        self.validation_errors = []

        try:
            # Extract basic metadata
            workflow_id = yaml_data.get('id', str(uuid.uuid4()))
            name = yaml_data.get('name', 'Unnamed Workflow')
            version = yaml_data.get('version', '1.0.0')
            description = yaml_data.get('description', '')

            # Parse metadata
            author = yaml_data.get('author', '')
            created_at = yaml_data.get('created_at', datetime.now().isoformat())
            tags = set(yaml_data.get('tags', []))

            # Parse variables
            variables = self._parse_variables(yaml_data.get('variables', {}))

            # Parse steps
            steps, step_order = self._parse_steps(yaml_data.get('steps', {}))

            # Parse global configuration
            global_config = yaml_data.get('config', {})
            global_timeout = global_config.get('timeout', 3600.0)
            global_retry_config = self._parse_retry_config(global_config.get('retry'))
            parallel_execution = global_config.get('parallel_execution', False)
            max_concurrent_steps = global_config.get('max_concurrent_steps', 5)

            # Parse agent configuration
            agent_config = yaml_data.get('agents', {})
            required_capabilities = agent_config.get('required_capabilities', [])
            preferred_agents = agent_config.get('preferred_agents', [])
            agent_selection_strategy = AgentSelectionStrategy(
                agent_config.get('selection_strategy', 'capability_match')
            )

            # Parse integration settings
            integration = yaml_data.get('integration', {})
            pheromone_integration = integration.get('pheromone_integration', True)
            progress_reporting = integration.get('progress_reporting', True)
            detailed_logging = integration.get('detailed_logging', True)

            # Legacy compatibility - parse phases if present
            if 'phases' in yaml_data:
                legacy_phases = self._parse_legacy_phases(yaml_data['phases'])
                # Convert phases to steps
                for phase in legacy_phases:
                    step = phase.to_workflow_step()
                    steps[step.id] = step
                    if step.id not in step_order:
                        step_order.append(step.id)

            # Validate the workflow
            workflow = WorkflowDefinition(
                id=workflow_id,
                name=name,
                version=version,
                description=description,
                author=author,
                created_at=created_at,
                tags=tags,
                variables=variables,
                steps=steps,
                step_order=step_order,
                global_timeout=global_timeout,
                global_retry_config=global_retry_config,
                parallel_execution=parallel_execution,
                max_concurrent_steps=max_concurrent_steps,
                required_capabilities=required_capabilities,
                preferred_agents=preferred_agents,
                agent_selection_strategy=agent_selection_strategy,
                pheromone_integration=pheromone_integration,
                progress_reporting=progress_reporting,
                detailed_logging=detailed_logging,
                # Legacy fields
                type=yaml_data.get('type', 'bmad'),
                project_types=yaml_data.get('project_types', []),
                total_duration_minutes=yaml_data.get('total_duration_minutes', 0),
                flow_diagram=yaml_data.get('flow_diagram', ''),
                metadata=yaml_data.get('metadata', {})
            )

            self._validate_workflow(workflow)

            if self.validation_errors:
                raise WorkflowValidationError(
                    "Workflow validation failed",
                    self.validation_errors
                )

            return workflow

        except WorkflowValidationError:
            raise
        except Exception as e:
            raise WorkflowValidationError(f"Error parsing workflow: {e}")

    def _parse_variables(self, variables_data: Dict[str, Any]) -> Dict[str, WorkflowVariable]:
        """Parse workflow variables"""
        variables = {}

        for name, var_data in variables_data.items():
            if isinstance(var_data, dict):
                variable = WorkflowVariable(
                    name=name,
                    value=var_data.get('value'),
                    type=var_data.get('type', 'string'),
                    description=var_data.get('description', ''),
                    required=var_data.get('required', False),
                    default=var_data.get('default')
                )
            else:
                # Simple value
                variable = WorkflowVariable(
                    name=name,
                    value=var_data,
                    type=type(var_data).__name__
                )

            variables[name] = variable

        return variables

    def _parse_steps(self, steps_data: Dict[str, Any]) -> Tuple[Dict[str, WorkflowStep], List[str]]:
        """Parse workflow steps"""
        steps = {}
        step_order = []

        for step_id, step_data in steps_data.items():
            try:
                step = self._parse_single_step(step_id, step_data)
                steps[step_id] = step
                step_order.append(step_id)
            except Exception as e:
                self.validation_errors.append(f"Error parsing step '{step_id}': {e}")

        return steps, step_order

    def _parse_single_step(self, step_id: str, step_data: Dict[str, Any]) -> WorkflowStep:
        """Parse a single workflow step"""
        name = step_data.get('name', step_id)
        step_type = StepType(step_data.get('type', 'task'))
        description = step_data.get('description', '')

        # Parse execution configuration
        command = step_data.get('command')
        parameters = step_data.get('parameters', {})
        environment = step_data.get('environment', {})

        # Parse conditional execution
        condition = self._parse_condition(step_data.get('condition'))
        depends_on = step_data.get('depends_on', [])
        if isinstance(depends_on, str):
            depends_on = [depends_on]

        # Parse agent requirements
        agent_requirements = self._parse_agent_requirements(step_data.get('agent'))
        assigned_agent = step_data.get('assigned_agent')

        # Parse error handling
        optional = step_data.get('optional', False)
        retry_config = self._parse_retry_config(step_data.get('retry'))
        timeout_config = self._parse_timeout_config(step_data.get('timeout'))
        on_failure = step_data.get('on_failure')
        on_success = step_data.get('on_success')

        # Parse parallel execution
        parallel_steps = step_data.get('parallel_steps', [])
        wait_for_all = step_data.get('wait_for_all', True)

        # Parse loop configuration
        loop_variable = step_data.get('loop_variable')
        loop_items = step_data.get('loop_items', [])
        loop_condition = self._parse_condition(step_data.get('loop_condition'))
        max_iterations = step_data.get('max_iterations', 100)

        # Parse output handling
        output_variables = step_data.get('output_variables', {})
        capture_output = step_data.get('capture_output', True)

        # Parse metadata
        tags = set(step_data.get('tags', []))
        priority = step_data.get('priority', 0)
        estimated_duration = step_data.get('estimated_duration', 60.0)

        return WorkflowStep(
            id=step_id,
            name=name,
            type=step_type,
            description=description,
            command=command,
            parameters=parameters,
            environment=environment,
            condition=condition,
            depends_on=depends_on,
            agent_requirements=agent_requirements,
            assigned_agent=assigned_agent,
            optional=optional,
            retry_config=retry_config,
            timeout_config=timeout_config,
            on_failure=on_failure,
            on_success=on_success,
            parallel_steps=parallel_steps,
            wait_for_all=wait_for_all,
            loop_variable=loop_variable,
            loop_items=loop_items,
            loop_condition=loop_condition,
            max_iterations=max_iterations,
            output_variables=output_variables,
            capture_output=capture_output,
            tags=tags,
            priority=priority,
            estimated_duration=estimated_duration
        )

    def _parse_condition(self, condition_data: Any) -> Optional[StepCondition]:
        """Parse step condition with support for complex conditions"""
        if not condition_data:
            return None

        if isinstance(condition_data, str):
            # Simple existence check
            return StepCondition(
                variable=condition_data,
                operator=ConditionOperator.EXISTS,
                value=True
            )

        if isinstance(condition_data, dict):
            # Handle complex conditions
            if 'conditions' in condition_data or 'sub_conditions' in condition_data:
                return self._parse_complex_condition(condition_data)

            # Simple condition
            variable = condition_data.get('variable', '')
            operator_str = condition_data.get('operator', 'eq')

            # Validate operator
            try:
                operator = ConditionOperator(operator_str)
            except ValueError:
                logger.warning(f"Unknown condition operator: {operator_str}, defaulting to 'eq'")
                operator = ConditionOperator.EQUALS

            value = condition_data.get('value')
            description = condition_data.get('description', '')
            negate = condition_data.get('negate', False)

            return StepCondition(
                variable=variable,
                operator=operator,
                value=value,
                description=description,
                negate=negate
            )

        if isinstance(condition_data, list):
            # List of conditions - treat as AND by default
            sub_conditions = []
            for item in condition_data:
                parsed = self._parse_condition(item)
                if parsed:
                    sub_conditions.append(parsed)

            if sub_conditions:
                return StepCondition(
                    variable="",
                    operator=ConditionOperator.AND,
                    value=None,
                    sub_conditions=sub_conditions
                )

        return None

    def _parse_complex_condition(self, condition_data: Dict[str, Any]) -> Optional[StepCondition]:
        """Parse complex condition with logical operators"""
        operator_str = condition_data.get('operator', 'and')

        try:
            operator = ConditionOperator(operator_str)
        except ValueError:
            logger.warning(f"Unknown logical operator: {operator_str}, defaulting to 'and'")
            operator = ConditionOperator.AND

        # Parse sub-conditions
        sub_conditions = []
        conditions_data = condition_data.get('conditions', condition_data.get('sub_conditions', []))

        for sub_data in conditions_data:
            parsed = self._parse_condition(sub_data)
            if parsed:
                sub_conditions.append(parsed)

        if not sub_conditions:
            logger.warning("Complex condition has no valid sub-conditions")
            return None

        description = condition_data.get('description', '')
        negate = condition_data.get('negate', False)

        return StepCondition(
            variable="",
            operator=operator,
            value=None,
            description=description,
            sub_conditions=sub_conditions,
            negate=negate
        )

    def _parse_agent_requirements(self, agent_data: Any) -> Optional[AgentRequirement]:
        """Parse agent requirements"""
        if not agent_data:
            return None

        if isinstance(agent_data, str):
            # Simple agent preference
            return AgentRequirement(
                preferred_agents=[agent_data]
            )

        if isinstance(agent_data, dict):
            capabilities = agent_data.get('capabilities', [])
            min_performance = agent_data.get('min_performance', 0.0)
            max_load = agent_data.get('max_load', 1.0)
            preferred_agents = agent_data.get('preferred_agents', [])
            excluded_agents = agent_data.get('excluded_agents', [])
            selection_strategy = AgentSelectionStrategy(
                agent_data.get('selection_strategy', 'capability_match')
            )

            return AgentRequirement(
                capabilities=capabilities,
                min_performance=min_performance,
                max_load=max_load,
                preferred_agents=preferred_agents,
                excluded_agents=excluded_agents,
                selection_strategy=selection_strategy
            )

        return None

    def _parse_retry_config(self, retry_data: Any) -> Optional[RetryConfig]:
        """Parse retry configuration"""
        if not retry_data:
            return None

        if isinstance(retry_data, int):
            # Simple max attempts
            return RetryConfig(max_attempts=retry_data)

        if isinstance(retry_data, dict):
            return RetryConfig(
                max_attempts=retry_data.get('max_attempts', 3),
                delay_seconds=retry_data.get('delay_seconds', 1.0),
                backoff_multiplier=retry_data.get('backoff_multiplier', 2.0),
                max_delay_seconds=retry_data.get('max_delay_seconds', 60.0),
                retry_on_errors=retry_data.get('retry_on_errors', [])
            )

        return None

    def _parse_timeout_config(self, timeout_data: Any) -> Optional[TimeoutConfig]:
        """Parse timeout configuration"""
        if not timeout_data:
            return None

        if isinstance(timeout_data, (int, float)):
            # Simple execution timeout
            return TimeoutConfig(execution_timeout=float(timeout_data))

        if isinstance(timeout_data, dict):
            return TimeoutConfig(
                execution_timeout=timeout_data.get('execution_timeout', 300.0),
                agent_response_timeout=timeout_data.get('agent_response_timeout', 30.0),
                total_timeout=timeout_data.get('total_timeout', 3600.0)
            )

        return None

    def _parse_legacy_phases(self, phases_data: List[Dict[str, Any]]) -> List[WorkflowPhase]:
        """Parse legacy workflow phases for backward compatibility"""
        phases = []

        for phase_data in phases_data:
            try:
                phase = WorkflowPhase(
                    id=phase_data.get('id', str(uuid.uuid4())),
                    name=phase_data.get('name', 'Unnamed Phase'),
                    description=phase_data.get('description', ''),
                    agent=phase_data.get('agent', ''),
                    creates=phase_data.get('creates', []),
                    requires=phase_data.get('requires', []),
                    optional_steps=phase_data.get('optional_steps', []),
                    duration_minutes=phase_data.get('duration_minutes', 30),
                    notes=phase_data.get('notes', ''),
                    condition=phase_data.get('condition')
                )
                phases.append(phase)
            except Exception as e:
                self.validation_errors.append(f"Error parsing legacy phase: {e}")

        return phases

    def _validate_workflow(self, workflow: WorkflowDefinition) -> None:
        """Validate workflow definition"""
        # Check required fields
        if not workflow.name:
            self.validation_errors.append("Workflow name is required")

        if not workflow.steps and not workflow.phases:
            self.validation_errors.append("Workflow must have at least one step or phase")

        # Validate step dependencies
        for step_id, step in workflow.steps.items():
            for dep in step.depends_on:
                if dep not in workflow.steps:
                    self.validation_errors.append(
                        f"Step '{step_id}' depends on non-existent step '{dep}'"
                    )

        # Check for circular dependencies
        self._check_circular_dependencies(workflow.steps)

        # Validate conditions reference existing variables
        for step_id, step in workflow.steps.items():
            if step.condition:
                if step.condition.variable not in workflow.variables:
                    self.validation_errors.append(
                        f"Step '{step_id}' condition references undefined variable '{step.condition.variable}'"
                    )

        # Validate agent requirements
        for step_id, step in workflow.steps.items():
            if step.agent_requirements:
                # Check if required capabilities are reasonable
                if len(step.agent_requirements.capabilities) > 10:
                    self.validation_errors.append(
                        f"Step '{step_id}' has too many required capabilities (max 10)"
                    )

    def _check_circular_dependencies(self, steps: Dict[str, WorkflowStep]) -> None:
        """Check for circular dependencies in workflow steps"""
        def has_cycle(step_id: str, visited: Set[str], rec_stack: Set[str]) -> bool:
            visited.add(step_id)
            rec_stack.add(step_id)

            step = steps.get(step_id)
            if step:
                for dep in step.depends_on:
                    if dep not in visited:
                        if has_cycle(dep, visited, rec_stack):
                            return True
                    elif dep in rec_stack:
                        return True

            rec_stack.remove(step_id)
            return False

        visited = set()
        for step_id in steps:
            if step_id not in visited:
                if has_cycle(step_id, visited, set()):
                    self.validation_errors.append(
                        f"Circular dependency detected involving step '{step_id}'"
                    )

class WorkflowTemplateManager:
    """Manages workflow templates and template selection"""

    def __init__(self):
        self.templates: Dict[str, WorkflowDefinition] = {}
        self.template_categories: Dict[str, List[str]] = {}
        self.custom_templates: Dict[str, WorkflowDefinition] = {}
        self._load_builtin_templates()

    def _load_builtin_templates(self):
        """Load built-in BMAD workflow templates"""
        # Load templates from BMAD-METHOD workflows
        builtin_templates = {
            'greenfield-fullstack': self._create_greenfield_fullstack_template(),
            'greenfield-service': self._create_greenfield_service_template(),
            'greenfield-ui': self._create_greenfield_ui_template(),
            'brownfield-fullstack': self._create_brownfield_fullstack_template(),
            'brownfield-service': self._create_brownfield_service_template(),
            'brownfield-ui': self._create_brownfield_ui_template(),
            'game-prototype': self._create_game_prototype_template(),
            'api-service': self._create_api_service_template(),
            'mobile-app': self._create_mobile_app_template(),
            'desktop-app': self._create_desktop_app_template()
        }

        self.templates.update(builtin_templates)

        # Categorize templates
        self.template_categories = {
            'greenfield': ['greenfield-fullstack', 'greenfield-service', 'greenfield-ui'],
            'brownfield': ['brownfield-fullstack', 'brownfield-service', 'brownfield-ui'],
            'specialized': ['game-prototype', 'api-service', 'mobile-app', 'desktop-app'],
            'web': ['greenfield-fullstack', 'greenfield-ui', 'brownfield-fullstack', 'brownfield-ui'],
            'backend': ['greenfield-service', 'brownfield-service', 'api-service'],
            'mobile': ['mobile-app'],
            'desktop': ['desktop-app'],
            'gaming': ['game-prototype']
        }

    def get_template(self, template_id: str) -> Optional[WorkflowDefinition]:
        """Get a workflow template by ID"""
        return self.templates.get(template_id) or self.custom_templates.get(template_id)

    def list_templates(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """List available templates, optionally filtered by category"""
        all_templates = {**self.templates, **self.custom_templates}

        if category and category in self.template_categories:
            template_ids = self.template_categories[category]
            filtered_templates = {tid: all_templates[tid] for tid in template_ids if tid in all_templates}
        else:
            filtered_templates = all_templates

        return [
            {
                'id': tid,
                'name': template.name,
                'description': template.description,
                'version': template.version,
                'author': template.author,
                'tags': list(template.tags),
                'project_types': getattr(template, 'project_types', []),
                'estimated_duration': sum(step.estimated_duration for step in template.steps.values()) / 60,  # in minutes
                'complexity': self._calculate_complexity(template)
            }
            for tid, template in filtered_templates.items()
        ]

    def search_templates(self, query: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search templates by query and project type"""
        all_templates = self.list_templates()

        results = []
        query_lower = query.lower()

        for template_info in all_templates:
            # Check if query matches name, description, or tags
            matches_query = (
                query_lower in template_info['name'].lower() or
                query_lower in template_info['description'].lower() or
                any(query_lower in tag.lower() for tag in template_info['tags'])
            )

            # Check project type if specified
            matches_project_type = (
                not project_type or
                project_type in template_info['project_types'] or
                any(project_type in pt for pt in template_info['project_types'])
            )

            if matches_query and matches_project_type:
                results.append(template_info)

        # Sort by relevance (exact matches first, then partial matches)
        results.sort(key=lambda t: (
            query_lower not in t['name'].lower(),  # Exact name matches first
            query_lower not in t['description'].lower(),  # Then description matches
            t['complexity']  # Then by complexity (simpler first)
        ))

        return results

    def add_custom_template(self, template_id: str, template: WorkflowDefinition):
        """Add a custom workflow template"""
        self.custom_templates[template_id] = template

    def remove_custom_template(self, template_id: str) -> bool:
        """Remove a custom workflow template"""
        if template_id in self.custom_templates:
            del self.custom_templates[template_id]
            return True
        return False

    def _calculate_complexity(self, template: WorkflowDefinition) -> int:
        """Calculate template complexity score"""
        complexity = 0
        complexity += len(template.steps)  # Number of steps
        complexity += sum(1 for step in template.steps.values() if step.condition)  # Conditional steps
        complexity += sum(len(step.depends_on) for step in template.steps.values())  # Dependencies
        complexity += sum(1 for step in template.steps.values() if step.parallel_steps)  # Parallel steps
        return complexity

    def _create_greenfield_fullstack_template(self) -> WorkflowDefinition:
        """Create greenfield fullstack development template"""
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Requirements Analysis",
                type=StepType.TASK,
                description="Analyze requirements and create project brief",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "analyst"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "documentation"],
                    preferred_agents=["analyst"]
                ),
                estimated_duration=1800.0,  # 30 minutes
                tags={"analysis", "documentation"}
            ),
            "prd_creation": WorkflowStep(
                id="prd_creation",
                name="Product Requirements Document",
                type=StepType.TASK,
                description="Create comprehensive PRD from project brief",
                command="create_document",
                parameters={
                    "template": "prd-tmpl",
                    "output": "docs/prd.md",
                    "agent_role": "pm",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["product_management", "documentation"],
                    preferred_agents=["pm"]
                ),
                estimated_duration=2400.0,  # 40 minutes
                tags={"product_management", "documentation"}
            ),
            "ux_specification": WorkflowStep(
                id="ux_specification",
                name="UX/UI Specification",
                type=StepType.TASK,
                description="Create frontend specification and user experience design",
                command="create_document",
                parameters={
                    "template": "front-end-spec-tmpl",
                    "output": "docs/front-end-spec.md",
                    "agent_role": "ux-expert",
                    "inputs": ["docs/prd.md"]
                },
                depends_on=["prd_creation"],
                agent_requirements=AgentRequirement(
                    capabilities=["ux_design", "frontend", "documentation"],
                    preferred_agents=["ux-expert"]
                ),
                estimated_duration=3000.0,  # 50 minutes
                tags={"ux_design", "frontend", "documentation"}
            ),
            "architecture_design": WorkflowStep(
                id="architecture_design",
                name="System Architecture",
                type=StepType.TASK,
                description="Design system architecture and technical specifications",
                command="create_document",
                parameters={
                    "template": "fullstack-architecture-tmpl",
                    "output": "docs/architecture.md",
                    "agent_role": "architect",
                    "inputs": ["docs/prd.md", "docs/front-end-spec.md"]
                },
                depends_on=["prd_creation", "ux_specification"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "system_design", "documentation"],
                    preferred_agents=["architect"]
                ),
                estimated_duration=3600.0,  # 60 minutes
                tags={"architecture", "system_design", "documentation"}
            ),
            "validation": WorkflowStep(
                id="validation",
                name="Product Owner Validation",
                type=StepType.CONDITION,
                description="Validate all artifacts with product owner checklist",
                command="validate_documents",
                parameters={
                    "checklist": "po-master-checklist",
                    "documents": ["docs/project-brief.md", "docs/prd.md", "docs/front-end-spec.md", "docs/architecture.md"],
                    "agent_role": "po"
                },
                depends_on=["architecture_design"],
                agent_requirements=AgentRequirement(
                    capabilities=["product_ownership", "validation"],
                    preferred_agents=["po"]
                ),
                condition=StepCondition(
                    variable="validation_required",
                    operator=ConditionOperator.EQUALS,
                    value=True
                ),
                estimated_duration=1200.0,  # 20 minutes
                tags={"validation", "quality_assurance"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete workflow for creating a new fullstack application from scratch",
            author="BMAD Method",
            tags={"greenfield", "fullstack", "web", "complete"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=7200.0,  # 2 hours
            parallel_execution=False,
            max_concurrent_steps=2,
            required_capabilities=["analysis", "architecture", "backend", "frontend"],
            pheromone_integration=True,
            progress_reporting=True,
            project_types=["fullstack", "web", "greenfield"]
        )

    def _create_greenfield_service_template(self) -> WorkflowDefinition:
        """Create greenfield service development template"""
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Service Requirements Analysis",
                type=StepType.TASK,
                description="Analyze service requirements and create project brief",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "analyst",
                    "focus": "backend_service"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "backend", "documentation"],
                    preferred_agents=["analyst"]
                ),
                estimated_duration=1200.0,  # 20 minutes
                tags={"analysis", "backend", "service"}
            ),
            "api_design": WorkflowStep(
                id="api_design",
                name="API Design",
                type=StepType.TASK,
                description="Design API endpoints and data models",
                command="create_document",
                parameters={
                    "template": "api-spec-tmpl",
                    "output": "docs/api-spec.md",
                    "agent_role": "architect",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["api_design", "backend", "documentation"],
                    preferred_agents=["architect"]
                ),
                estimated_duration=2400.0,  # 40 minutes
                tags={"api_design", "backend", "architecture"}
            ),
            "implementation": WorkflowStep(
                id="implementation",
                name="Service Implementation",
                type=StepType.TASK,
                description="Implement the backend service",
                command="implement_service",
                parameters={
                    "framework": "fastapi",
                    "database": "postgresql",
                    "agent_role": "developer",
                    "inputs": ["docs/api-spec.md"]
                },
                depends_on=["api_design"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend_development", "python", "api"],
                    preferred_agents=["developer"]
                ),
                estimated_duration=5400.0,  # 90 minutes
                tags={"implementation", "backend", "development"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-service",
            name="Greenfield Service Development",
            version="1.0.0",
            description="Workflow for creating a new backend service from scratch",
            author="BMAD Method",
            tags={"greenfield", "service", "backend", "api"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=5400.0,  # 90 minutes
            parallel_execution=False,
            required_capabilities=["analysis", "backend", "api"],
            project_types=["service", "backend", "api", "greenfield"]
        )

    def _create_greenfield_ui_template(self) -> WorkflowDefinition:
        """Create greenfield UI development template"""
        steps = {
            "ux_analysis": WorkflowStep(
                id="ux_analysis",
                name="UX Requirements Analysis",
                type=StepType.TASK,
                description="Analyze UI/UX requirements",
                command="create_document",
                parameters={
                    "template": "project-brief-tmpl",
                    "output": "docs/project-brief.md",
                    "agent_role": "ux-expert",
                    "focus": "frontend_ui"
                },
                agent_requirements=AgentRequirement(
                    capabilities=["ux_design", "frontend", "analysis"],
                    preferred_agents=["ux-expert"]
                ),
                estimated_duration=1800.0,  # 30 minutes
                tags={"ux_design", "frontend", "analysis"}
            ),
            "ui_implementation": WorkflowStep(
                id="ui_implementation",
                name="UI Implementation",
                type=StepType.TASK,
                description="Implement the user interface",
                command="implement_ui",
                parameters={
                    "framework": "react",
                    "styling": "tailwind",
                    "agent_role": "frontend-dev",
                    "inputs": ["docs/project-brief.md"]
                },
                depends_on=["ux_analysis"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend_development", "react", "ui"],
                    preferred_agents=["frontend-dev"]
                ),
                estimated_duration=4800.0,  # 80 minutes
                tags={"implementation", "frontend", "ui"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-ui",
            name="Greenfield UI Development",
            version="1.0.0",
            description="Workflow for creating a new user interface from scratch",
            author="BMAD Method",
            tags={"greenfield", "ui", "frontend", "react"},
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600.0,  # 60 minutes
            parallel_execution=False,
            required_capabilities=["ux_design", "frontend"],
            project_types=["ui", "frontend", "greenfield"]
        )

    def _create_brownfield_fullstack_template(self) -> WorkflowDefinition:
        """Create brownfield fullstack enhancement template"""
        # Simplified brownfield template - would be expanded in full implementation
        steps = {
            "analysis": WorkflowStep(
                id="analysis",
                name="Existing System Analysis",
                type=StepType.TASK,
                description="Analyze existing system and enhancement requirements",
                command="analyze_existing_system",
                parameters={"agent_role": "architect"},
                estimated_duration=2400.0,
                tags={"analysis", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-fullstack",
            name="Brownfield Fullstack Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing fullstack applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "fullstack", "enhancement"]
        )

    def _create_brownfield_service_template(self) -> WorkflowDefinition:
        """Create brownfield service enhancement template"""
        steps = {
            "service_analysis": WorkflowStep(
                id="service_analysis",
                name="Service Analysis",
                type=StepType.TASK,
                description="Analyze existing service for enhancements",
                command="analyze_service",
                parameters={"agent_role": "architect"},
                estimated_duration=1800.0,
                tags={"analysis", "service", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-service",
            name="Brownfield Service Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing backend services",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "service", "enhancement"]
        )

    def _create_brownfield_ui_template(self) -> WorkflowDefinition:
        """Create brownfield UI enhancement template"""
        steps = {
            "ui_analysis": WorkflowStep(
                id="ui_analysis",
                name="UI Analysis",
                type=StepType.TASK,
                description="Analyze existing UI for improvements",
                command="analyze_ui",
                parameters={"agent_role": "ux-expert"},
                estimated_duration=1500.0,
                tags={"analysis", "ui", "brownfield"}
            )
        }

        return WorkflowDefinition(
            id="brownfield-ui",
            name="Brownfield UI Enhancement",
            version="1.0.0",
            description="Workflow for enhancing existing user interfaces",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["brownfield", "ui", "enhancement"]
        )

    def _create_game_prototype_template(self) -> WorkflowDefinition:
        """Create game prototype development template"""
        steps = {
            "concept": WorkflowStep(
                id="concept",
                name="Game Concept",
                type=StepType.TASK,
                description="Define game concept and mechanics",
                command="create_game_concept",
                parameters={"agent_role": "game-designer"},
                estimated_duration=1800.0,
                tags={"concept", "game", "design"}
            )
        }

        return WorkflowDefinition(
            id="game-prototype",
            name="Game Prototype Development",
            version="1.0.0",
            description="Rapid game prototyping workflow",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["game", "prototype", "gaming"]
        )

    def _create_api_service_template(self) -> WorkflowDefinition:
        """Create API service development template"""
        steps = {
            "api_design": WorkflowStep(
                id="api_design",
                name="API Design",
                type=StepType.TASK,
                description="Design RESTful API endpoints",
                command="design_api",
                parameters={"agent_role": "architect"},
                estimated_duration=2400.0,
                tags={"api", "design", "backend"}
            )
        }

        return WorkflowDefinition(
            id="api-service",
            name="API Service Development",
            version="1.0.0",
            description="Workflow for creating RESTful API services",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["api", "service", "backend"]
        )

    def _create_mobile_app_template(self) -> WorkflowDefinition:
        """Create mobile app development template"""
        steps = {
            "mobile_design": WorkflowStep(
                id="mobile_design",
                name="Mobile App Design",
                type=StepType.TASK,
                description="Design mobile application interface and flow",
                command="design_mobile_app",
                parameters={"agent_role": "mobile-designer"},
                estimated_duration=3600.0,
                tags={"mobile", "design", "app"}
            )
        }

        return WorkflowDefinition(
            id="mobile-app",
            name="Mobile App Development",
            version="1.0.0",
            description="Workflow for creating mobile applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["mobile", "app", "ios", "android"]
        )

    def _create_desktop_app_template(self) -> WorkflowDefinition:
        """Create desktop app development template"""
        steps = {
            "desktop_design": WorkflowStep(
                id="desktop_design",
                name="Desktop App Design",
                type=StepType.TASK,
                description="Design desktop application interface",
                command="design_desktop_app",
                parameters={"agent_role": "desktop-designer"},
                estimated_duration=3000.0,
                tags={"desktop", "design", "app"}
            )
        }

        return WorkflowDefinition(
            id="desktop-app",
            name="Desktop App Development",
            version="1.0.0",
            description="Workflow for creating desktop applications",
            steps=steps,
            step_order=list(steps.keys()),
            project_types=["desktop", "app", "electron"]
        )


class WorkflowExecutionEngine:
    """Enhanced workflow execution engine with comprehensive features"""

    def __init__(self, pheromone_bus=None):
        self.pheromone_bus = pheromone_bus
        self.active_executions: Dict[str, WorkflowExecution] = {}
        self.agent_registry: Dict[str, Dict[str, Any]] = {}
        self.execution_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.template_manager = WorkflowTemplateManager()
        self.monitor = WorkflowMonitor(pheromone_bus)

        # Performance tracking
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "average_execution_time": 0.0
        }

    def get_available_templates(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available workflow templates"""
        return self.template_manager.list_templates(category)

    def search_templates(self, query: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for workflow templates"""
        return self.template_manager.search_templates(query, project_type)

    def get_template_by_id(self, template_id: str) -> Optional[WorkflowDefinition]:
        """Get a specific workflow template"""
        return self.template_manager.get_template(template_id)

    def recommend_template(self, project_description: str, project_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Recommend workflow templates based on project description"""
        # Simple keyword-based recommendation
        keywords = project_description.lower().split()

        # Score templates based on keyword matches
        templates = self.get_available_templates()
        scored_templates = []

        for template in templates:
            score = 0

            # Check name matches
            for keyword in keywords:
                if keyword in template['name'].lower():
                    score += 3
                if keyword in template['description'].lower():
                    score += 2
                if keyword in [tag.lower() for tag in template['tags']]:
                    score += 1

            # Boost score for project type match
            if project_type and project_type in template['project_types']:
                score += 5

            if score > 0:
                template_copy = template.copy()
                template_copy['recommendation_score'] = score
                scored_templates.append(template_copy)

        # Sort by score (highest first)
        scored_templates.sort(key=lambda t: t['recommendation_score'], reverse=True)

        return scored_templates[:5]  # Return top 5 recommendations

    def create_custom_template(self, template_data: Dict[str, Any]) -> str:
        """Create a custom workflow template"""
        template_id = template_data.get('id', f"custom_{uuid.uuid4().hex[:8]}")

        # Parse template data into WorkflowDefinition
        parser = WorkflowYAMLParser()
        workflow = parser.parse_yaml_data(template_data)

        # Add to template manager
        self.template_manager.add_custom_template(template_id, workflow)

        return template_id

    def delete_custom_template(self, template_id: str) -> bool:
        """Delete a custom workflow template"""
        return self.template_manager.remove_custom_template(template_id)

    def get_execution_monitoring_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get real-time monitoring data for an execution"""
        return self.monitor.get_monitoring_data(execution_id)

    def subscribe_to_monitoring_updates(self, callback: Callable):
        """Subscribe to real-time monitoring updates"""
        self.monitor.subscribe_to_updates(callback)

    def unsubscribe_from_monitoring_updates(self, callback: Callable):
        """Unsubscribe from monitoring updates"""
        self.monitor.unsubscribe_from_updates(callback)

    def get_all_active_executions(self) -> List[Dict[str, Any]]:
        """Get status of all active executions"""
        active_executions = []

        for execution_id, execution in self.active_executions.items():
            monitoring_data = self.get_execution_monitoring_data(execution_id)
            if monitoring_data:
                active_executions.append(monitoring_data)

        return active_executions

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get comprehensive execution statistics"""
        with self.execution_lock:
            stats = self.execution_stats.copy()

        # Add current active execution count
        stats["active_executions"] = len(self.active_executions)

        # Add monitoring statistics
        stats["active_monitors"] = len(self.monitor.active_monitors)

        return stats

    def generate_workflow_diagram(self, workflow: WorkflowDefinition, execution: Optional[WorkflowExecution] = None) -> str:
        """Generate Mermaid diagram for workflow visualization"""
        mermaid_lines = ["graph TD"]

        # Add nodes for each step
        for step_id, step in workflow.steps.items():
            # Determine node style based on step type and status
            node_style = self._get_node_style(step, execution)
            node_label = f"{step.name}"

            # Add status indicator if execution is provided
            if execution and step_id in execution.step_status:
                status = execution.step_status[step_id]
                status_icon = self._get_status_icon(status)
                node_label = f"{status_icon} {node_label}"

            mermaid_lines.append(f'    {step_id}["{node_label}"]{node_style}')

        # Add edges for dependencies
        for step_id, step in workflow.steps.items():
            for dep_id in step.depends_on:
                if dep_id in workflow.steps:
                    mermaid_lines.append(f"    {dep_id} --> {step_id}")

        # Add parallel execution indicators
        for step_id, step in workflow.steps.items():
            if step.parallel_steps:
                for parallel_step in step.parallel_steps:
                    if parallel_step in workflow.steps:
                        mermaid_lines.append(f"    {step_id} -.-> {parallel_step}")

        # Add conditional paths
        for step_id, step in workflow.steps.items():
            if step.condition:
                condition_node = f"{step_id}_condition"
                mermaid_lines.append(f'    {condition_node}{{"{step.condition.description or "Condition"}"}}')
                mermaid_lines.append(f"    {condition_node} -->|Yes| {step_id}")

                # Add dependencies to condition node
                for dep_id in step.depends_on:
                    if dep_id in workflow.steps:
                        mermaid_lines.append(f"    {dep_id} --> {condition_node}")

        # Add styling
        mermaid_lines.extend([
            "",
            "    classDef pending fill:#f9f9f9,stroke:#333,stroke-width:2px",
            "    classDef running fill:#fff3cd,stroke:#856404,stroke-width:3px",
            "    classDef completed fill:#d4edda,stroke:#155724,stroke-width:2px",
            "    classDef failed fill:#f8d7da,stroke:#721c24,stroke-width:2px",
            "    classDef skipped fill:#e2e3e5,stroke:#6c757d,stroke-width:1px",
            "    classDef condition fill:#e7f3ff,stroke:#0066cc,stroke-width:2px"
        ])

        return "\n".join(mermaid_lines)

    def _get_node_style(self, step: WorkflowStep, execution: Optional[WorkflowExecution]) -> str:
        """Get Mermaid node style based on step type and status"""
        if not execution:
            return ":::pending"

        status = execution.step_status.get(step.id, StepStatus.PENDING)

        style_map = {
            StepStatus.PENDING: ":::pending",
            StepStatus.RUNNING: ":::running",
            StepStatus.COMPLETED: ":::completed",
            StepStatus.FAILED: ":::failed",
            StepStatus.SKIPPED: ":::skipped",
            StepStatus.OPTIONAL_FAILED: ":::skipped"
        }

        return style_map.get(status, ":::pending")

    def _get_status_icon(self, status: StepStatus) -> str:
        """Get status icon for visualization"""
        icon_map = {
            StepStatus.PENDING: "⏳",
            StepStatus.RUNNING: "🔄",
            StepStatus.COMPLETED: "✅",
            StepStatus.FAILED: "❌",
            StepStatus.SKIPPED: "⏭️",
            StepStatus.OPTIONAL_FAILED: "⚠️",
            StepStatus.RETRYING: "🔁"
        }

        return icon_map.get(status, "⏳")

    def get_workflow_progress(self, execution: WorkflowExecution) -> Dict[str, Any]:
        """Get detailed workflow progress information"""
        total_steps = len(execution.step_status)
        if total_steps == 0:
            return {"overall_progress": 0.0, "phase_progress": [], "step_details": []}

        # Calculate overall progress
        completed_steps = sum(1 for status in execution.step_status.values()
                            if status in [StepStatus.COMPLETED, StepStatus.SKIPPED])
        overall_progress = (completed_steps / total_steps) * 100

        # Get step details with progress
        step_details = []
        for step_id, status in execution.step_status.items():
            step_info = {
                "id": step_id,
                "name": step_id.replace("_", " ").title(),
                "status": status.value,
                "progress": 100 if status in [StepStatus.COMPLETED, StepStatus.SKIPPED] else
                          50 if status == StepStatus.RUNNING else 0,
                "start_time": execution.step_start_times.get(step_id),
                "end_time": execution.step_end_times.get(step_id),
                "duration": self._calculate_step_duration(execution, step_id),
                "result": execution.step_results.get(step_id, {})
            }
            step_details.append(step_info)

        # Calculate estimated completion time
        estimated_completion = self._estimate_completion_time(execution)

        return {
            "overall_progress": overall_progress,
            "completed_steps": completed_steps,
            "total_steps": total_steps,
            "failed_steps": execution.failed_steps,
            "skipped_steps": execution.skipped_steps,
            "step_details": step_details,
            "estimated_completion": estimated_completion,
            "elapsed_time": time.time() - execution.started_at if execution.started_at else 0
        }

    def _calculate_step_duration(self, execution: WorkflowExecution, step_id: str) -> Optional[float]:
        """Calculate duration of a step"""
        start_time = execution.step_start_times.get(step_id)
        end_time = execution.step_end_times.get(step_id)

        if start_time and end_time:
            return end_time - start_time
        elif start_time:
            return time.time() - start_time

        return None

    def _estimate_completion_time(self, execution: WorkflowExecution) -> Optional[float]:
        """Estimate workflow completion time"""
        if not execution.started_at:
            return None

        elapsed_time = time.time() - execution.started_at
        total_steps = len(execution.step_status)
        completed_steps = sum(1 for status in execution.step_status.values()
                            if status in [StepStatus.COMPLETED, StepStatus.SKIPPED])

        if completed_steps == 0:
            return None

        # Simple linear estimation based on current progress
        progress_ratio = completed_steps / total_steps
        estimated_total_time = elapsed_time / progress_ratio
        estimated_remaining = estimated_total_time - elapsed_time

        return max(0, estimated_remaining)


@dataclass
class ParallelExecutionState:
    """State management for parallel workflow execution"""
    pending_steps: Set[str]
    running_tasks: Dict[str, asyncio.Task]
    completed_steps: Set[str]
    failed_steps: Set[str]
    max_concurrent: int
    resource_manager: 'ResourceManager'


class ResourceManager:
    """Manages computational resources for parallel execution"""

    def __init__(self):
        self.cpu_usage = 0.0
        self.memory_usage = 0.0
        self.allocated_resources: Dict[str, Dict[str, float]] = {}
        self.max_cpu_usage = 0.8  # 80% max CPU usage
        self.max_memory_usage = 0.8  # 80% max memory usage

    def can_allocate_resources(self, step: WorkflowStep) -> bool:
        """Check if resources can be allocated for a step"""
        # Estimate resource requirements based on step type and parameters
        estimated_cpu = self._estimate_cpu_requirement(step)
        estimated_memory = self._estimate_memory_requirement(step)

        return (self.cpu_usage + estimated_cpu <= self.max_cpu_usage and
                self.memory_usage + estimated_memory <= self.max_memory_usage)

    def allocate_resources(self, step: WorkflowStep):
        """Allocate resources for a step"""
        cpu_req = self._estimate_cpu_requirement(step)
        memory_req = self._estimate_memory_requirement(step)

        self.allocated_resources[step.id] = {
            'cpu': cpu_req,
            'memory': memory_req
        }

        self.cpu_usage += cpu_req
        self.memory_usage += memory_req

    def free_resources(self, step_id: str):
        """Free resources allocated to a step"""
        if step_id in self.allocated_resources:
            resources = self.allocated_resources.pop(step_id)
            self.cpu_usage -= resources['cpu']
            self.memory_usage -= resources['memory']

            # Ensure usage doesn't go negative due to floating point errors
            self.cpu_usage = max(0.0, self.cpu_usage)
            self.memory_usage = max(0.0, self.memory_usage)

    def update_availability(self):
        """Update resource availability (could integrate with system monitoring)"""
        # This could be enhanced to check actual system resources
        pass

    def _estimate_cpu_requirement(self, step: WorkflowStep) -> float:
        """Estimate CPU requirement for a step"""
        # Base CPU requirement
        base_cpu = 0.1

        # Adjust based on step type
        if step.type == StepType.PARALLEL:
            base_cpu *= 1.5
        elif step.type == StepType.LOOP:
            base_cpu *= 2.0

        # Adjust based on estimated duration (longer tasks need more CPU)
        if step.estimated_duration > 3600:  # > 1 hour
            base_cpu *= 1.5

        return min(base_cpu, 0.3)  # Cap at 30% CPU per step

    def _estimate_memory_requirement(self, step: WorkflowStep) -> float:
        """Estimate memory requirement for a step"""
        # Base memory requirement
        base_memory = 0.05  # 5% of system memory

        # Adjust based on step parameters
        if 'large_dataset' in step.parameters:
            base_memory *= 3.0
        elif 'memory_intensive' in step.parameters:
            base_memory *= 2.0

        return min(base_memory, 0.2)  # Cap at 20% memory per step

    async def _execute_step_with_cleanup(self, step: WorkflowStep, execution: WorkflowExecution, workflow: WorkflowDefinition, state: ParallelExecutionState) -> bool:
        """Execute a step with automatic resource cleanup"""
        try:
            result = await self._execute_step(step, execution, workflow)
            return result
        finally:
            # Always free resources when step completes
            state.resource_manager.free_resources(step.id)

    async def _detect_deadlock(self, state: ParallelExecutionState, workflow: WorkflowDefinition) -> bool:
        """Detect potential deadlock in parallel execution"""
        if not state.pending_steps or state.running_tasks:
            return False

        # Check if any pending step can ever be satisfied
        for step_id in state.pending_steps:
            step = workflow.steps[step_id]
            can_be_satisfied = True

            for dep_id in step.depends_on:
                if dep_id in state.failed_steps:
                    # Dependency failed and step is not optional
                    if not step.optional:
                        can_be_satisfied = False
                        break
                elif dep_id in state.pending_steps:
                    # Circular dependency check
                    if self._has_circular_dependency(step_id, dep_id, workflow, state.pending_steps):
                        can_be_satisfied = False
                        break

            if can_be_satisfied:
                return False

        return True  # All pending steps are blocked

    def _has_circular_dependency(self, step_id: str, dep_id: str, workflow: WorkflowDefinition, pending_steps: Set[str], visited: Optional[Set[str]] = None) -> bool:
        """Check for circular dependencies"""
        if visited is None:
            visited = set()

        if dep_id == step_id:
            return True

        if dep_id in visited or dep_id not in pending_steps:
            return False

        visited.add(dep_id)
        dep_step = workflow.steps.get(dep_id)

        if dep_step:
            for transitive_dep in dep_step.depends_on:
                if self._has_circular_dependency(step_id, transitive_dep, workflow, pending_steps, visited):
                    return True

        visited.remove(dep_id)
        return False

    async def _resolve_deadlock(self, state: ParallelExecutionState, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Attempt to resolve deadlock by skipping optional steps or failing gracefully"""
        logger.warning("Attempting deadlock resolution")

        # Find optional steps that can be skipped
        skippable_steps = []
        for step_id in state.pending_steps:
            step = workflow.steps[step_id]
            if step.optional:
                skippable_steps.append(step_id)

        # Skip optional steps to break deadlock
        for step_id in skippable_steps:
            logger.info(f"Skipping optional step {step_id} to resolve deadlock")
            state.pending_steps.remove(step_id)
            execution.step_status[step_id] = StepStatus.SKIPPED
            execution.skipped_steps += 1
            state.completed_steps.add(step_id)

        # If no optional steps to skip, mark remaining as failed
        if not skippable_steps and state.pending_steps:
            logger.error("Cannot resolve deadlock - marking remaining steps as failed")
            for step_id in list(state.pending_steps):
                state.pending_steps.remove(step_id)
                execution.step_status[step_id] = StepStatus.FAILED
                execution.failed_steps += 1
                state.failed_steps.add(step_id)

    async def _cleanup_remaining_tasks(self, state: ParallelExecutionState, execution: WorkflowExecution):
        """Clean up any remaining running tasks"""
        if state.running_tasks:
            logger.info(f"Waiting for {len(state.running_tasks)} remaining tasks to complete")

            try:
                # Wait for all remaining tasks with a reasonable timeout
                await asyncio.wait_for(
                    asyncio.gather(*state.running_tasks.values(), return_exceptions=True),
                    timeout=30.0  # 30 second timeout for cleanup
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within cleanup timeout")

                # Cancel remaining tasks
                for step_id, task in state.running_tasks.items():
                    if not task.done():
                        task.cancel()
                        execution.step_status[step_id] = StepStatus.FAILED
                        execution.failed_steps += 1

            # Free all remaining resources
            for step_id in state.running_tasks:
                state.resource_manager.free_resources(step_id)


class WorkflowMonitor:
    """Real-time workflow monitoring and status tracking"""

    def __init__(self, pheromone_bus=None):
        self.pheromone_bus = pheromone_bus
        self.active_monitors: Dict[str, 'ExecutionMonitor'] = {}
        self.monitoring_enabled = True
        self.update_interval = 1.0  # seconds
        self.subscribers: List[Callable] = []

    def start_monitoring(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> str:
        """Start monitoring a workflow execution"""
        monitor_id = f"monitor_{execution.id}"

        monitor = ExecutionMonitor(
            execution_id=execution.id,
            workflow_id=workflow.id,
            workflow_name=workflow.name,
            total_steps=len(workflow.steps),
            pheromone_bus=self.pheromone_bus
        )

        self.active_monitors[monitor_id] = monitor
        monitor.start()

        logger.info(f"Started monitoring for execution {execution.id}")
        return monitor_id

    def stop_monitoring(self, monitor_id: str):
        """Stop monitoring a workflow execution"""
        if monitor_id in self.active_monitors:
            monitor = self.active_monitors.pop(monitor_id)
            monitor.stop()
            logger.info(f"Stopped monitoring {monitor_id}")

    def update_execution_status(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Update execution status for all relevant monitors"""
        for monitor in self.active_monitors.values():
            if monitor.execution_id == execution.id:
                monitor.update_status(execution, workflow)

    def get_monitoring_data(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Get current monitoring data for an execution"""
        for monitor in self.active_monitors.values():
            if monitor.execution_id == execution_id:
                return monitor.get_current_status()
        return None

    def subscribe_to_updates(self, callback: Callable):
        """Subscribe to real-time monitoring updates"""
        self.subscribers.append(callback)

    def unsubscribe_from_updates(self, callback: Callable):
        """Unsubscribe from monitoring updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)

    def broadcast_update(self, update_data: Dict[str, Any]):
        """Broadcast update to all subscribers"""
        for callback in self.subscribers:
            try:
                callback(update_data)
            except Exception as e:
                logger.error(f"Error broadcasting update: {e}")


@dataclass
class ExecutionMonitor:
    """Individual execution monitor with real-time tracking"""
    execution_id: str
    workflow_id: str
    workflow_name: str
    total_steps: int
    pheromone_bus: Any = None

    # Monitoring state
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)
    update_count: int = 0
    is_active: bool = True

    # Progress tracking
    completed_steps: int = 0
    failed_steps: int = 0
    skipped_steps: int = 0
    running_steps: int = 0

    # Performance metrics
    step_durations: Dict[str, float] = field(default_factory=dict)
    step_start_times: Dict[str, float] = field(default_factory=dict)
    throughput_history: List[float] = field(default_factory=list)

    def start(self):
        """Start monitoring"""
        self.is_active = True
        self.start_time = time.time()
        logger.debug(f"Started monitoring execution {self.execution_id}")

    def stop(self):
        """Stop monitoring"""
        self.is_active = False
        logger.debug(f"Stopped monitoring execution {self.execution_id}")

    def update_status(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Update monitoring status with current execution state"""
        if not self.is_active:
            return

        current_time = time.time()
        self.last_update = current_time
        self.update_count += 1

        # Update step counts
        self.completed_steps = sum(1 for status in execution.step_status.values()
                                 if status == StepStatus.COMPLETED)
        self.failed_steps = sum(1 for status in execution.step_status.values()
                              if status == StepStatus.FAILED)
        self.skipped_steps = sum(1 for status in execution.step_status.values()
                               if status == StepStatus.SKIPPED)
        self.running_steps = sum(1 for status in execution.step_status.values()
                               if status == StepStatus.RUNNING)

        # Update step durations
        for step_id, start_time in execution.step_start_times.items():
            if step_id in execution.step_end_times:
                end_time = execution.step_end_times[step_id]
                self.step_durations[step_id] = end_time - start_time
            elif step_id not in self.step_start_times:
                self.step_start_times[step_id] = start_time

        # Calculate throughput (steps per minute)
        elapsed_time = current_time - self.start_time
        if elapsed_time > 0:
            throughput = (self.completed_steps / elapsed_time) * 60  # steps per minute
            self.throughput_history.append(throughput)

            # Keep only last 10 measurements
            if len(self.throughput_history) > 10:
                self.throughput_history.pop(0)

        # Send pheromone update if available
        if self.pheromone_bus:
            self._send_pheromone_update(execution, workflow)

    def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        elapsed_time = time.time() - self.start_time
        progress_percentage = (self.completed_steps / self.total_steps * 100) if self.total_steps > 0 else 0

        # Calculate average throughput
        avg_throughput = sum(self.throughput_history) / len(self.throughput_history) if self.throughput_history else 0

        # Estimate completion time
        remaining_steps = self.total_steps - self.completed_steps - self.failed_steps - self.skipped_steps
        estimated_completion = None
        if avg_throughput > 0 and remaining_steps > 0:
            estimated_completion = (remaining_steps / avg_throughput) * 60  # seconds

        return {
            "execution_id": self.execution_id,
            "workflow_id": self.workflow_id,
            "workflow_name": self.workflow_name,
            "status": {
                "progress_percentage": progress_percentage,
                "completed_steps": self.completed_steps,
                "failed_steps": self.failed_steps,
                "skipped_steps": self.skipped_steps,
                "running_steps": self.running_steps,
                "total_steps": self.total_steps
            },
            "timing": {
                "elapsed_time": elapsed_time,
                "estimated_completion": estimated_completion,
                "last_update": self.last_update,
                "update_count": self.update_count
            },
            "performance": {
                "average_throughput": avg_throughput,
                "step_durations": dict(self.step_durations),
                "throughput_history": list(self.throughput_history)
            }
        }

    def _send_pheromone_update(self, execution: WorkflowExecution, workflow: WorkflowDefinition):
        """Send monitoring update via pheromone system"""
        try:
            update_data = {
                "type": "workflow_monitoring_update",
                "execution_id": self.execution_id,
                "workflow_id": self.workflow_id,
                "progress": self.completed_steps / self.total_steps if self.total_steps > 0 else 0,
                "status": execution.status.value,
                "timestamp": time.time()
            }

            # This would integrate with the actual pheromone system
            # self.pheromone_bus.emit("monitoring_update", update_data)

        except Exception as e:
            logger.error(f"Failed to send pheromone update: {e}")

    async def start_workflow(
        self,
        workflow_id: str,
        project_id: str,
        agent_team: Dict[str, Any],
        pheromone_bus: Dict[str, Any],
        context: Dict[str, Any]
    ) -> WorkflowExecution:
        """Start workflow execution with enhanced context"""

        # Load workflow definition
        workflow = self.get_registered_workflow(workflow_id)
        if not workflow:
            workflow = await self._load_workflow_definition(workflow_id)
        if not workflow:
            raise WorkflowExecutionError(f"Workflow '{workflow_id}' not found")

        # Create execution instance
        execution = WorkflowExecution(
            id=str(uuid.uuid4()),
            workflow_id=workflow_id,
            status=WorkflowStatus.RUNNING,
            started_at=time.time(),
            project_id=project_id,
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )

        # Initialize execution context
        await self._initialize_execution_context(execution, workflow, context)

        # Register execution
        with self.execution_lock:
            self.active_executions[execution.id] = execution
            self.execution_stats["total_executions"] += 1

        # Start monitoring
        monitor_id = self.monitor.start_monitoring(execution, workflow)
        execution.monitor_id = monitor_id

        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, workflow))

        logger.info(f"Started workflow execution {execution.id} for workflow {workflow_id}")
        return execution

    async def _load_workflow_definition(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Load workflow definition by ID"""
        # First check if it's a built-in workflow
        builtin_workflow = await self._get_builtin_workflow(workflow_id)
        if builtin_workflow:
            return builtin_workflow

        # Try to load from various sources
        workflow_paths = [
            Path(f"workflows/{workflow_id}.yaml"),
            Path(f"workflows/{workflow_id}.yml"),
            Path(f"src/workflows/{workflow_id}.yaml"),
            Path(f"config/workflows/{workflow_id}.yaml")
        ]

        parser = WorkflowYAMLParser()

        for path in workflow_paths:
            if path.exists():
                try:
                    return parser.parse_workflow_file(path)
                except Exception as e:
                    logger.warning(f"Failed to load workflow from {path}: {e}")

        return None

    def register_workflow(self, workflow: WorkflowDefinition) -> None:
        """Register a workflow definition for execution"""
        self._registered_workflows = getattr(self, '_registered_workflows', {})
        self._registered_workflows[workflow.id] = workflow

    def get_registered_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get a registered workflow definition"""
        registered = getattr(self, '_registered_workflows', {})
        return registered.get(workflow_id)

    async def _get_builtin_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get built-in workflow definition"""
        builtin_workflows = {
            "greenfield-fullstack": self._create_greenfield_fullstack_workflow(),
            "enhancement": self._create_enhancement_workflow(),
            "debugging": self._create_debugging_workflow(),
            "testing": self._create_testing_workflow()
        }

        return builtin_workflows.get(workflow_id)

    def _create_greenfield_fullstack_workflow(self) -> WorkflowDefinition:
        """Create built-in greenfield fullstack workflow"""
        steps = {
            "analyze_requirements": WorkflowStep(
                id="analyze_requirements",
                name="Analyze Requirements",
                type=StepType.TASK,
                description="Analyze project requirements and create specifications",
                agent_requirements=AgentRequirement(
                    capabilities=["analysis", "requirements"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"requirements": "analysis_result"}
            ),
            "design_architecture": WorkflowStep(
                id="design_architecture",
                name="Design Architecture",
                type=StepType.TASK,
                description="Design system architecture and technology stack",
                depends_on=["analyze_requirements"],
                agent_requirements=AgentRequirement(
                    capabilities=["architecture", "design"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"architecture": "design_result"}
            ),
            "generate_backend": WorkflowStep(
                id="generate_backend",
                name="Generate Backend",
                type=StepType.TASK,
                description="Generate backend code and API endpoints",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["backend", "api", "database"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"backend_files": "backend_result"}
            ),
            "generate_frontend": WorkflowStep(
                id="generate_frontend",
                name="Generate Frontend",
                type=StepType.TASK,
                description="Generate frontend code and user interface",
                depends_on=["design_architecture"],
                agent_requirements=AgentRequirement(
                    capabilities=["frontend", "ui", "react"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=900.0,  # 15 minutes
                output_variables={"frontend_files": "frontend_result"}
            ),
            "integrate_components": WorkflowStep(
                id="integrate_components",
                name="Integrate Components",
                type=StepType.TASK,
                description="Integrate frontend and backend components",
                depends_on=["generate_backend", "generate_frontend"],
                agent_requirements=AgentRequirement(
                    capabilities=["integration", "fullstack"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"integration_result": "integration_status"}
            ),
            "generate_tests": WorkflowStep(
                id="generate_tests",
                name="Generate Tests",
                type=StepType.TASK,
                description="Generate comprehensive test suites",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["testing", "qa"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=600.0,  # 10 minutes
                output_variables={"test_files": "test_result"}
            ),
            "create_documentation": WorkflowStep(
                id="create_documentation",
                name="Create Documentation",
                type=StepType.TASK,
                description="Create project documentation and README",
                depends_on=["integrate_components"],
                optional=True,
                agent_requirements=AgentRequirement(
                    capabilities=["documentation", "writing"],
                    selection_strategy=AgentSelectionStrategy.CAPABILITY_MATCH
                ),
                estimated_duration=300.0,  # 5 minutes
                output_variables={"documentation": "docs_result"}
            )
        }

        return WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Fullstack Development",
            version="1.0.0",
            description="Complete workflow for creating a new fullstack application from scratch",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=3600.0,  # 1 hour
            parallel_execution=True,
            max_concurrent_steps=3,
            required_capabilities=["analysis", "architecture", "backend", "frontend"],
            pheromone_integration=True,
            progress_reporting=True
        )

    def _create_enhancement_workflow(self) -> WorkflowDefinition:
        """Create built-in enhancement workflow"""
        steps = {
            "analyze_existing": WorkflowStep(
                id="analyze_existing",
                name="Analyze Existing Code",
                type=StepType.TASK,
                description="Analyze existing codebase and identify enhancement opportunities",
                agent_requirements=AgentRequirement(capabilities=["analysis", "code_review"]),
                estimated_duration=600.0
            ),
            "plan_enhancements": WorkflowStep(
                id="plan_enhancements",
                name="Plan Enhancements",
                type=StepType.TASK,
                description="Create enhancement plan and implementation strategy",
                depends_on=["analyze_existing"],
                agent_requirements=AgentRequirement(capabilities=["planning", "architecture"]),
                estimated_duration=300.0
            ),
            "implement_changes": WorkflowStep(
                id="implement_changes",
                name="Implement Changes",
                type=StepType.TASK,
                description="Implement the planned enhancements",
                depends_on=["plan_enhancements"],
                agent_requirements=AgentRequirement(capabilities=["development", "refactoring"]),
                estimated_duration=1200.0
            ),
            "test_changes": WorkflowStep(
                id="test_changes",
                name="Test Changes",
                type=StepType.TASK,
                description="Test the implemented changes",
                depends_on=["implement_changes"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=600.0
            )
        }

        return WorkflowDefinition(
            id="enhancement",
            name="Code Enhancement Workflow",
            version="1.0.0",
            description="Workflow for enhancing existing codebases",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=2400.0,  # 40 minutes
            required_capabilities=["analysis", "development", "testing"]
        )

    def _create_debugging_workflow(self) -> WorkflowDefinition:
        """Create built-in debugging workflow"""
        steps = {
            "identify_issue": WorkflowStep(
                id="identify_issue",
                name="Identify Issue",
                type=StepType.TASK,
                description="Identify and analyze the reported issue",
                agent_requirements=AgentRequirement(capabilities=["debugging", "analysis"]),
                estimated_duration=300.0
            ),
            "reproduce_bug": WorkflowStep(
                id="reproduce_bug",
                name="Reproduce Bug",
                type=StepType.TASK,
                description="Reproduce the bug in a controlled environment",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["debugging", "testing"]),
                estimated_duration=600.0,
                optional=True
            ),
            "fix_issue": WorkflowStep(
                id="fix_issue",
                name="Fix Issue",
                type=StepType.TASK,
                description="Implement fix for the identified issue",
                depends_on=["identify_issue"],
                agent_requirements=AgentRequirement(capabilities=["development", "debugging"]),
                estimated_duration=900.0
            ),
            "verify_fix": WorkflowStep(
                id="verify_fix",
                name="Verify Fix",
                type=StepType.TASK,
                description="Verify that the fix resolves the issue",
                depends_on=["fix_issue"],
                agent_requirements=AgentRequirement(capabilities=["testing", "qa"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="debugging",
            name="Bug Fixing Workflow",
            version="1.0.0",
            description="Workflow for identifying and fixing bugs",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1800.0,  # 30 minutes
            required_capabilities=["debugging", "development", "testing"]
        )

    def _create_testing_workflow(self) -> WorkflowDefinition:
        """Create built-in testing workflow"""
        steps = {
            "analyze_code": WorkflowStep(
                id="analyze_code",
                name="Analyze Code for Testing",
                type=StepType.TASK,
                description="Analyze code to determine testing strategy",
                agent_requirements=AgentRequirement(capabilities=["analysis", "testing"]),
                estimated_duration=300.0
            ),
            "generate_unit_tests": WorkflowStep(
                id="generate_unit_tests",
                name="Generate Unit Tests",
                type=StepType.TASK,
                description="Generate comprehensive unit tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "unit_tests"]),
                estimated_duration=600.0
            ),
            "generate_integration_tests": WorkflowStep(
                id="generate_integration_tests",
                name="Generate Integration Tests",
                type=StepType.TASK,
                description="Generate integration tests",
                depends_on=["analyze_code"],
                agent_requirements=AgentRequirement(capabilities=["testing", "integration_tests"]),
                estimated_duration=600.0,
                optional=True
            ),
            "run_tests": WorkflowStep(
                id="run_tests",
                name="Run Test Suite",
                type=StepType.TASK,
                description="Execute all generated tests",
                depends_on=["generate_unit_tests"],
                agent_requirements=AgentRequirement(capabilities=["testing", "execution"]),
                estimated_duration=300.0
            )
        }

        return WorkflowDefinition(
            id="testing",
            name="Comprehensive Testing Workflow",
            version="1.0.0",
            description="Workflow for generating and running comprehensive tests",
            steps=steps,
            step_order=list(steps.keys()),
            global_timeout=1500.0,  # 25 minutes
            required_capabilities=["testing", "analysis"]
        )

    async def _initialize_execution_context(
        self,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition,
        context: Dict[str, Any]
    ) -> None:
        """Initialize execution context with variables and state"""

        # Initialize workflow variables
        for name, var in workflow.variables.items():
            execution.variables[name] = var

        # Add context variables
        for name, value in context.items():
            if name not in execution.variables:
                execution.variables[name] = WorkflowVariable(
                    name=name,
                    value=value,
                    type=type(value).__name__
                )

        # Initialize step status
        for step_id in workflow.steps:
            execution.step_status[step_id] = StepStatus.PENDING
            execution.step_attempts[step_id] = 0

        # Set total steps
        execution.total_steps = len(workflow.steps)

        logger.info(f"Initialized execution context for {execution.id}")

    async def _execute_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition, context: Dict[str, Any] = None) -> None:
        """Main workflow execution loop"""

        try:
            logger.info(f"Starting workflow execution {execution.id}")

            # Drop pheromone for workflow start
            await self._drop_pheromone("workflow_started", {
                "execution_id": execution.id,
                "workflow_id": workflow.id,
                "workflow_name": workflow.name
            }, execution.project_id)

            # Check if this is a project generation workflow
            if workflow.id in ["project_generation", "fullstack", "web_application", "api_service", "mobile_application"]:
                await self._execute_project_generation_workflow(execution, workflow, context or {})
            else:
                # Execute steps based on workflow configuration
                if workflow.parallel_execution:
                    await self._execute_parallel_workflow(execution, workflow)
                else:
                    await self._execute_sequential_workflow(execution, workflow)

            # Check final status
            if execution.failed_steps > 0:
                execution.status = WorkflowStatus.FAILED
                execution.last_error = f"Workflow failed with {execution.failed_steps} failed steps"
            else:
                execution.status = WorkflowStatus.COMPLETED

            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            # Update monitoring
            self.monitor.update_execution_status(execution, workflow)

            # Update statistics
            with self.execution_lock:
                if execution.status == WorkflowStatus.COMPLETED:
                    self.execution_stats["successful_executions"] += 1
                else:
                    self.execution_stats["failed_executions"] += 1

                # Update average execution time
                total_executions = self.execution_stats["total_executions"]
                current_avg = self.execution_stats["average_execution_time"]
                new_avg = ((current_avg * (total_executions - 1)) + execution.execution_time) / total_executions
                self.execution_stats["average_execution_time"] = new_avg

            # Drop completion pheromone
            await self._drop_pheromone("workflow_completed", {
                "execution_id": execution.id,
                "status": execution.status.value,
                "execution_time": execution.execution_time,
                "completed_steps": execution.completed_steps,
                "failed_steps": execution.failed_steps
            }, execution.project_id)

            logger.info(f"Workflow execution {execution.id} completed with status {execution.status}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = str(e)
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at

            logger.error(f"Workflow execution {execution.id} failed: {e}")

            # Drop error pheromone
            await self._drop_pheromone("workflow_failed", {
                "execution_id": execution.id,
                "error": str(e),
                "execution_time": execution.execution_time
            }, execution.project_id)

        finally:
            # Clean up execution
            with self.execution_lock:
                self.active_executions.pop(execution.id, None)

    async def _execute_project_generation_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition, context: Dict[str, Any]) -> None:
        """Execute project generation workflow using the ProjectGenerationPipeline"""
        try:
            # Import the project generator
            from .project_generator import ProjectGenerationPipeline
            from .project_types import ProjectType, GenerationConfig

            logger.info(f"Starting project generation workflow for execution {execution.id}")

            # Extract context information
            prompt = context.get("prompt", "Generate a software project")
            project_name = context.get("project_name", "Generated Project")
            project_type = context.get("project_type", "web_application")
            project_path = context.get("project_path", f"./projects/{execution.project_id}")

            # Create generation configuration
            config = GenerationConfig(
                project_type=ProjectType(project_type),
                include_tests=context.get("include_tests", True),
                include_docs=context.get("include_docs", True),
                include_ci_cd=context.get("include_ci_cd", True),
                include_docker=context.get("include_docker", False),
                include_deployment=context.get("include_deployment", False),
                code_quality_level=context.get("code_quality_level", "standard"),
                security_level=context.get("security_level", "standard")
            )

            # Initialize and run the project generation pipeline
            pipeline = ProjectGenerationPipeline(config)

            # Drop pheromone for project generation start
            await self._drop_pheromone("project_generation_started", {
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "project_type": project_type,
                "prompt": prompt
            }, execution.project_id)

            # Execute the project generation
            result = await pipeline.generate_project(
                prompt=prompt,
                project_name=project_name,
                project_type=project_type,
                project_path=project_path,
                workflow=workflow.id
            )

            # Update execution status based on result
            if result.get("success", False):
                execution.status = WorkflowStatus.COMPLETED
                execution.completed_steps = 8  # All phases completed
                execution.total_steps = 8

                # Drop pheromone for successful completion
                await self._drop_pheromone("project_generation_completed", {
                    "execution_id": execution.id,
                    "project_id": execution.project_id,
                    "total_files": result.get("total_files", 0),
                    "project_path": result.get("project_path", project_path)
                }, execution.project_id)

                logger.info(f"Project generation completed successfully for execution {execution.id}")
            else:
                execution.status = WorkflowStatus.FAILED
                execution.last_error = result.get("error", "Project generation failed")
                execution.failed_steps = 1

                # Drop pheromone for failure
                await self._drop_pheromone("project_generation_failed", {
                    "execution_id": execution.id,
                    "project_id": execution.project_id,
                    "error": execution.last_error
                }, execution.project_id)

                logger.error(f"Project generation failed for execution {execution.id}: {execution.last_error}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.last_error = f"Project generation workflow error: {str(e)}"
            execution.failed_steps = 1

            logger.error(f"Project generation workflow failed for execution {execution.id}: {e}")

            # Drop pheromone for workflow error
            await self._drop_pheromone("project_generation_workflow_error", {
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "error": str(e)
            }, execution.project_id)

            raise e

    async def _execute_sequential_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps sequentially"""

        for step_id in workflow.step_order:
            step = workflow.steps[step_id]

            # Check if step should be executed
            if not await self._should_execute_step(step, execution, workflow):
                execution.step_status[step_id] = StepStatus.SKIPPED
                execution.skipped_steps += 1
                continue

            # Wait for dependencies
            await self._wait_for_dependencies(step, execution)

            # Execute step
            success = await self._execute_step(step, execution, workflow)

            if success:
                execution.completed_steps += 1
            else:
                execution.failed_steps += 1

                # Check if workflow should continue
                if not step.optional and not workflow.global_retry_config:
                    break

    async def _execute_parallel_workflow(self, execution: WorkflowExecution, workflow: WorkflowDefinition) -> None:
        """Execute workflow steps in parallel where possible"""

        pending_steps = set(workflow.step_order)
        running_tasks = {}
        max_concurrent = workflow.max_concurrent_steps
        max_iterations = 100  # Prevent infinite loops
        iteration = 0

        while (pending_steps or running_tasks) and iteration < max_iterations:
            iteration += 1

            # Start new tasks if we have capacity
            started_new_task = False
            while len(running_tasks) < max_concurrent and pending_steps:
                # Find a step that can be started
                ready_step = None
                for step_id in list(pending_steps):  # Create a copy to avoid modification during iteration
                    step = workflow.steps[step_id]
                    if await self._are_dependencies_satisfied(step, execution):
                        ready_step = step_id
                        break

                if ready_step:
                    step = workflow.steps[ready_step]
                    pending_steps.remove(ready_step)

                    # Check if step should be executed
                    if await self._should_execute_step(step, execution, workflow):
                        task = asyncio.create_task(self._execute_step(step, execution, workflow))
                        running_tasks[ready_step] = task
                        started_new_task = True
                    else:
                        execution.step_status[ready_step] = StepStatus.SKIPPED
                        execution.skipped_steps += 1
                        started_new_task = True
                else:
                    # No ready steps available
                    break

            # Wait for at least one task to complete if we have running tasks
            if running_tasks:
                try:
                    done, pending_tasks = await asyncio.wait(
                        running_tasks.values(),
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=1.0  # Add timeout to prevent hanging
                    )

                    # Process completed tasks
                    for task in done:
                        # Find which step this task belongs to
                        step_id = None
                        for sid, t in running_tasks.items():
                            if t == task:
                                step_id = sid
                                break

                        if step_id:
                            try:
                                success = await task
                                del running_tasks[step_id]

                                if success:
                                    execution.completed_steps += 1
                                else:
                                    execution.failed_steps += 1
                            except Exception as e:
                                logger.error(f"Task for step {step_id} failed: {e}")
                                del running_tasks[step_id]
                                execution.failed_steps += 1

                except asyncio.TimeoutError:
                    # Timeout waiting for tasks - continue to next iteration
                    pass
            elif not started_new_task and pending_steps:
                # No tasks running and no new tasks started - might be deadlock
                logger.warning(f"Potential deadlock detected. Pending steps: {pending_steps}")
                # Skip remaining steps to avoid infinite loop
                for step_id in pending_steps:
                    execution.step_status[step_id] = StepStatus.SKIPPED
                    execution.skipped_steps += 1
                break

        if iteration >= max_iterations:
            logger.warning("Parallel workflow execution reached maximum iterations")

        # Wait for any remaining tasks to complete
        if running_tasks:
            try:
                await asyncio.wait(running_tasks.values(), timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout")

    async def _should_execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Check if a step should be executed based on conditions"""

        # Check step condition
        if step.condition:
            if not await self._evaluate_condition(step.condition, execution):
                return False

        # Check dependencies
        if not await self._are_dependencies_satisfied(step, execution):
            return False

        return True

    async def _evaluate_condition(self, condition: StepCondition, execution: WorkflowExecution) -> bool:
        """Evaluate a step condition with support for complex logic"""

        try:
            result = await self._evaluate_single_condition(condition, execution)
            return not result if condition.negate else result
        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

    async def _evaluate_single_condition(self, condition: StepCondition, execution: WorkflowExecution) -> bool:
        """Evaluate a single condition"""

        # Handle logical operators
        if condition.operator == ConditionOperator.AND:
            for sub_condition in condition.sub_conditions:
                if not await self._evaluate_condition(sub_condition, execution):
                    return False
            return True

        elif condition.operator == ConditionOperator.OR:
            for sub_condition in condition.sub_conditions:
                if await self._evaluate_condition(sub_condition, execution):
                    return True
            return False

        elif condition.operator == ConditionOperator.NOT:
            if condition.sub_conditions:
                return not await self._evaluate_condition(condition.sub_conditions[0], execution)
            return True

        # Handle variable-based conditions
        variable = execution.variables.get(condition.variable)

        # Special handling for existence checks
        if condition.operator == ConditionOperator.EXISTS:
            return variable is not None
        elif condition.operator == ConditionOperator.NOT_EXISTS:
            return variable is None

        # If variable doesn't exist and we're not checking existence, return False
        if variable is None:
            return False

        var_value = variable.value
        condition_value = condition.value

        # Handle empty/not empty checks
        if condition.operator == ConditionOperator.IS_EMPTY:
            return not var_value or (isinstance(var_value, (list, dict, str)) and len(var_value) == 0)
        elif condition.operator == ConditionOperator.IS_NOT_EMPTY:
            return bool(var_value) and (not isinstance(var_value, (list, dict, str)) or len(var_value) > 0)

        # Convert values for comparison
        var_str = str(var_value) if var_value is not None else ""
        condition_str = str(condition_value) if condition_value is not None else ""

        # Evaluate based on operator
        if condition.operator == ConditionOperator.EQUALS:
            return var_value == condition_value
        elif condition.operator == ConditionOperator.NOT_EQUALS:
            return var_value != condition_value
        elif condition.operator == ConditionOperator.GREATER_THAN:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a > b)
        elif condition.operator == ConditionOperator.LESS_THAN:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a < b)
        elif condition.operator == ConditionOperator.GREATER_EQUAL:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a >= b)
        elif condition.operator == ConditionOperator.LESS_EQUAL:
            return self._safe_numeric_compare(var_value, condition_value, lambda a, b: a <= b)
        elif condition.operator == ConditionOperator.CONTAINS:
            return condition_str in var_str
        elif condition.operator == ConditionOperator.NOT_CONTAINS:
            return condition_str not in var_str
        elif condition.operator == ConditionOperator.STARTS_WITH:
            return var_str.startswith(condition_str)
        elif condition.operator == ConditionOperator.ENDS_WITH:
            return var_str.endswith(condition_str)
        elif condition.operator == ConditionOperator.IN_LIST:
            if isinstance(condition_value, list):
                return var_value in condition_value
            return False
        elif condition.operator == ConditionOperator.NOT_IN_LIST:
            if isinstance(condition_value, list):
                return var_value not in condition_value
            return True
        elif condition.operator == ConditionOperator.REGEX_MATCH:
            import re
            try:
                return bool(re.search(condition_str, var_str))
            except re.error:
                logger.warning(f"Invalid regex pattern: {condition_str}")
                return False

        return False

    def _safe_numeric_compare(self, a: Any, b: Any, compare_func: Callable) -> bool:
        """Safely compare numeric values"""
        try:
            # Try to convert to numbers
            if isinstance(a, (int, float)) and isinstance(b, (int, float)):
                return compare_func(a, b)

            # Try string to number conversion
            try:
                num_a = float(a) if '.' in str(a) else int(a)
                num_b = float(b) if '.' in str(b) else int(b)
                return compare_func(num_a, num_b)
            except (ValueError, TypeError):
                # Fall back to string comparison
                return compare_func(str(a), str(b))
        except Exception:
            return False

    async def _are_dependencies_satisfied(self, step: WorkflowStep, execution: WorkflowExecution) -> bool:
        """Check if all step dependencies are satisfied"""

        for dep_id in step.depends_on:
            dep_status = execution.step_status.get(dep_id, StepStatus.PENDING)
            if dep_status not in [StepStatus.COMPLETED, StepStatus.SKIPPED]:
                return False

        return True

    async def _wait_for_dependencies(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Wait for step dependencies to complete"""

        while not await self._are_dependencies_satisfied(step, execution):
            await asyncio.sleep(0.1)  # Small delay to prevent busy waiting

    async def _execute_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a single workflow step with retry logic"""

        execution.step_status[step.id] = StepStatus.RUNNING

        # Drop pheromone for step start
        await self._drop_pheromone("step_started", {
            "step_id": step.id,
            "step_name": step.name,
            "execution_id": execution.id
        }, execution.project_id)

        max_attempts = 1
        if step.retry_config:
            max_attempts = step.retry_config.max_attempts
        elif workflow.global_retry_config:
            max_attempts = workflow.global_retry_config.max_attempts

        for attempt in range(max_attempts):
            execution.step_attempts[step.id] = attempt + 1

            try:
                # Execute based on step type
                if step.type == StepType.TASK:
                    success = await self._execute_task_step(step, execution, workflow)
                elif step.type == StepType.CONDITION:
                    success = await self._execute_condition_step(step, execution, workflow)
                elif step.type == StepType.LOOP:
                    success = await self._execute_loop_step(step, execution, workflow)
                elif step.type == StepType.PARALLEL:
                    success = await self._execute_parallel_step(step, execution, workflow)
                elif step.type == StepType.AGENT_ASSIGNMENT:
                    success = await self._execute_agent_assignment_step(step, execution, workflow)
                elif step.type == StepType.PHEROMONE_DROP:
                    success = await self._execute_pheromone_step(step, execution, workflow)
                elif step.type == StepType.WAIT:
                    success = await self._execute_wait_step(step, execution, workflow)
                else:
                    success = await self._execute_custom_step(step, execution, workflow)

                if success:
                    execution.step_status[step.id] = StepStatus.COMPLETED

                    # Handle output variables
                    await self._handle_step_outputs(step, execution)

                    # Drop success pheromone
                    await self._drop_pheromone("step_completed", {
                        "step_id": step.id,
                        "step_name": step.name,
                        "execution_id": execution.id,
                        "attempt": attempt + 1
                    }, execution.project_id)

                    return True
                else:
                    # Step failed, check if we should retry
                    if attempt < max_attempts - 1:
                        execution.step_status[step.id] = StepStatus.RETRYING

                        # Calculate retry delay
                        delay = self._calculate_retry_delay(step, workflow, attempt)
                        await asyncio.sleep(delay)

                        # Drop retry pheromone
                        await self._drop_pheromone("step_retrying", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempt": attempt + 1,
                            "next_attempt_in": delay
                        }, execution.project_id)
                    else:
                        # All attempts failed
                        if step.optional:
                            execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        else:
                            execution.step_status[step.id] = StepStatus.FAILED

                        # Drop failure pheromone
                        await self._drop_pheromone("step_failed", {
                            "step_id": step.id,
                            "step_name": step.name,
                            "execution_id": execution.id,
                            "attempts": max_attempts,
                            "optional": step.optional
                        }, execution.project_id)

                        return step.optional  # Return True for optional steps

            except Exception as e:
                logger.error(f"Error executing step {step.id}: {e}")
                execution.last_error = str(e)

                if attempt < max_attempts - 1:
                    # Retry on exception
                    execution.step_status[step.id] = StepStatus.RETRYING
                    delay = self._calculate_retry_delay(step, workflow, attempt)
                    await asyncio.sleep(delay)
                else:
                    # Final failure
                    if step.optional:
                        execution.step_status[step.id] = StepStatus.OPTIONAL_FAILED
                        return True
                    else:
                        execution.step_status[step.id] = StepStatus.FAILED
                        return False

        return False

    def _calculate_retry_delay(self, step: WorkflowStep, workflow: WorkflowDefinition, attempt: int) -> float:
        """Calculate retry delay with exponential backoff"""

        retry_config = step.retry_config or workflow.global_retry_config
        if not retry_config:
            return 1.0

        delay = retry_config.delay_seconds * (retry_config.backoff_multiplier ** attempt)
        return min(delay, retry_config.max_delay_seconds)

    async def _execute_task_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a task step"""

        try:
            # Assign agent if needed
            agent_id = await self._assign_agent_to_step(step, execution)
            if not agent_id:
                logger.error(f"Failed to assign agent to step {step.id}")
                return False

            execution.agent_assignments[step.id] = agent_id

            # Prepare step context
            step_context = {
                "step_id": step.id,
                "step_name": step.name,
                "command": step.command,
                "parameters": step.parameters,
                "environment": step.environment,
                "execution_id": execution.id,
                "project_id": execution.project_id,
                "variables": {name: var.value for name, var in execution.variables.items()}
            }

            # Execute step through agent
            start_time = time.time()
            result = await self._execute_step_with_agent(agent_id, step_context)
            end_time = time.time()

            execution.agent_response_times[step.id] = end_time - start_time
            execution.step_results[step.id] = result

            return result.get("success", False) if isinstance(result, dict) else bool(result)

        except Exception as e:
            logger.error(f"Task step {step.id} execution failed: {e}")
            execution.step_results[step.id] = {"success": False, "error": str(e)}
            return False

    async def _execute_condition_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a condition step"""

        if step.condition:
            result = await self._evaluate_condition(step.condition, execution)
            execution.step_results[step.id] = {"condition_result": result}
            return result

        return True

    async def _execute_loop_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute a loop step"""

        try:
            iteration = 0
            loop_results = []

            # Loop over items if specified
            if step.loop_items:
                for item in step.loop_items:
                    if iteration >= step.max_iterations:
                        break

                    # Set loop variable
                    if step.loop_variable:
                        execution.variables[step.loop_variable] = WorkflowVariable(
                            name=step.loop_variable,
                            value=item,
                            type=type(item).__name__
                        )

                    # Execute loop body (would need to be defined in step parameters)
                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            # Loop with condition
            elif step.loop_condition:
                while iteration < step.max_iterations:
                    if not await self._evaluate_condition(step.loop_condition, execution):
                        break

                    loop_result = await self._execute_loop_iteration(step, execution, iteration)
                    loop_results.append(loop_result)

                    iteration += 1

            execution.step_results[step.id] = {
                "iterations": iteration,
                "results": loop_results
            }

            return True

        except Exception as e:
            logger.error(f"Loop step {step.id} execution failed: {e}")
            return False

    async def _execute_loop_iteration(self, step: WorkflowStep, execution: WorkflowExecution, iteration: int) -> Any:
        """Execute a single loop iteration"""

        # This would execute the loop body defined in step parameters
        # For now, return a simple result
        return {
            "iteration": iteration,
            "timestamp": time.time(),
            "success": True
        }

    async def _execute_parallel_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute parallel sub-steps"""

        if not step.parallel_steps:
            return True

        try:
            # Create tasks for parallel steps
            tasks = []
            for parallel_step_id in step.parallel_steps:
                if parallel_step_id in workflow.steps:
                    parallel_step = workflow.steps[parallel_step_id]
                    task = asyncio.create_task(
                        self._execute_step(parallel_step, execution, workflow)
                    )
                    tasks.append((parallel_step_id, task))

            # Wait for completion
            if step.wait_for_all:
                # Wait for all tasks to complete
                results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
                success = all(isinstance(result, bool) and result for result in results)
            else:
                # Wait for first successful completion
                done, pending = await asyncio.wait(
                    [task for _, task in tasks],
                    return_when=asyncio.FIRST_COMPLETED
                )

                # Cancel pending tasks
                for task in pending:
                    task.cancel()

                # Check if any task succeeded
                success = any(task.result() for task in done if not task.exception())

            execution.step_results[step.id] = {
                "parallel_steps": step.parallel_steps,
                "wait_for_all": step.wait_for_all,
                "success": success
            }

            return success

        except Exception as e:
            logger.error(f"Parallel step {step.id} execution failed: {e}")
            return False

    async def _execute_agent_assignment_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute agent assignment step"""

        try:
            # This step type is used to dynamically assign agents to subsequent steps
            agent_id = await self._assign_agent_to_step(step, execution)

            if agent_id:
                # Store agent assignment for future steps
                target_steps = step.parameters.get("target_steps", [])
                for target_step_id in target_steps:
                    execution.agent_assignments[target_step_id] = agent_id

                execution.step_results[step.id] = {
                    "assigned_agent": agent_id,
                    "target_steps": target_steps
                }
                return True

            return False

        except Exception as e:
            logger.error(f"Agent assignment step {step.id} failed: {e}")
            return False

    async def _execute_pheromone_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute pheromone drop step"""

        try:
            signal_type = step.parameters.get("signal_type", "custom")
            payload = step.parameters.get("payload", {})

            # Add execution context to payload
            payload.update({
                "step_id": step.id,
                "execution_id": execution.id,
                "timestamp": time.time()
            })

            await self._drop_pheromone(signal_type, payload, execution.project_id)

            execution.step_results[step.id] = {
                "signal_type": signal_type,
                "payload": payload
            }

            return True

        except Exception as e:
            logger.error(f"Pheromone step {step.id} failed: {e}")
            return False

    async def _execute_wait_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute wait step"""

        try:
            wait_time = step.parameters.get("wait_time", 1.0)
            wait_condition = step.parameters.get("wait_condition")
            max_wait_time = step.parameters.get("max_wait_time", 300.0)  # 5 minutes default

            start_time = time.time()

            if wait_condition:
                # Wait for condition to be true
                while time.time() - start_time < max_wait_time:
                    # Parse and evaluate wait condition
                    condition = StepCondition(
                        variable=wait_condition.get("variable", ""),
                        operator=ConditionOperator(wait_condition.get("operator", "exists")),
                        value=wait_condition.get("value", True)
                    )

                    if await self._evaluate_condition(condition, execution):
                        break

                    await asyncio.sleep(0.5)  # Check every 500ms
                else:
                    # Timeout
                    logger.warning(f"Wait step {step.id} timed out after {max_wait_time}s")
            else:
                # Simple time-based wait
                await asyncio.sleep(wait_time)

            execution.step_results[step.id] = {
                "wait_time": wait_time,
                "actual_wait_time": time.time() - start_time
            }

            return True

        except Exception as e:
            logger.error(f"Wait step {step.id} failed: {e}")
            return False

    async def _execute_custom_step(
        self,
        step: WorkflowStep,
        execution: WorkflowExecution,
        workflow: WorkflowDefinition
    ) -> bool:
        """Execute custom step type"""

        try:
            # Custom steps can be extended by subclassing
            # For now, treat as a basic task
            return await self._execute_task_step(step, execution, workflow)

        except Exception as e:
            logger.error(f"Custom step {step.id} failed: {e}")
            return False

    async def _assign_agent_to_step(self, step: WorkflowStep, execution: WorkflowExecution) -> Optional[str]:
        """Assign an agent to a step based on requirements"""

        # Check if agent is already assigned
        if step.assigned_agent:
            return step.assigned_agent

        # Check if agent was assigned by previous step
        if step.id in execution.agent_assignments:
            return execution.agent_assignments[step.id]

        # Use agent requirements to find suitable agent
        if step.agent_requirements:
            return await self._find_suitable_agent(step.agent_requirements, execution)

        # Fallback to any available agent
        return await self._get_any_available_agent(execution)

    async def _find_suitable_agent(
        self,
        requirements: AgentRequirement,
        execution: WorkflowExecution
    ) -> Optional[str]:
        """Find an agent that meets the requirements"""

        # Get available agents from agent team
        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if not available_agents:
            logger.warning("No agents available in agent team")
            return None

        # Filter agents based on requirements
        suitable_agents = []

        for agent in available_agents:
            agent_id = agent.get("id", "")
            agent_capabilities = set(agent.get("capabilities", []))
            required_capabilities = set(requirements.capabilities)

            # Check capabilities
            if required_capabilities and not required_capabilities.issubset(agent_capabilities):
                continue

            # Check excluded agents
            if agent_id in requirements.excluded_agents:
                continue

            # Check agent load
            current_load = execution.agent_loads.get(agent_id, 0.0)
            if current_load > requirements.max_load:
                continue

            suitable_agents.append(agent)

        if not suitable_agents:
            logger.warning(f"No suitable agents found for requirements: {requirements}")
            return None

        # Select agent based on strategy
        selected_agent = await self._select_agent_by_strategy(
            suitable_agents,
            requirements.selection_strategy,
            execution
        )

        if selected_agent:
            agent_id = selected_agent.get("id", "")
            # Update agent load
            execution.agent_loads[agent_id] = execution.agent_loads.get(agent_id, 0.0) + 0.1
            return agent_id

        return None

    async def _select_agent_by_strategy(
        self,
        agents: List[Dict[str, Any]],
        strategy: AgentSelectionStrategy,
        execution: WorkflowExecution
    ) -> Optional[Dict[str, Any]]:
        """Select agent based on selection strategy"""

        if not agents:
            return None

        if strategy == AgentSelectionStrategy.ROUND_ROBIN:
            # Simple round-robin selection
            total_assignments = sum(1 for agent_id in execution.agent_assignments.values())
            return agents[total_assignments % len(agents)]

        elif strategy == AgentSelectionStrategy.LEAST_LOADED:
            # Select agent with lowest current load
            min_load = float('inf')
            selected_agent = None

            for agent in agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent = agent

            return selected_agent

        elif strategy == AgentSelectionStrategy.RANDOM:
            import random
            return random.choice(agents)

        elif strategy == AgentSelectionStrategy.PRIORITY_BASED:
            # Select agent with highest priority
            return max(agents, key=lambda a: a.get("priority", 0))

        else:  # CAPABILITY_MATCH or default
            # Select first suitable agent
            return agents[0]

    async def _get_any_available_agent(self, execution: WorkflowExecution) -> Optional[str]:
        """Get any available agent as fallback"""

        agent_team = execution.agent_team or {}
        available_agents = agent_team.get("agents", [])

        if available_agents:
            # Return least loaded agent
            min_load = float('inf')
            selected_agent_id = None

            for agent in available_agents:
                agent_id = agent.get("id", "")
                load = execution.agent_loads.get(agent_id, 0.0)
                if load < min_load:
                    min_load = load
                    selected_agent_id = agent_id

            return selected_agent_id

        return None

    async def _execute_step_with_agent(self, agent_id: str, step_context: Dict[str, Any]) -> Any:
        """Execute step through assigned agent"""

        try:
            # This would integrate with the actual agent execution system
            # For now, simulate agent execution

            logger.info(f"Executing step {step_context['step_id']} with agent {agent_id}")

            # Simulate processing time
            await asyncio.sleep(0.1)

            # Return fallback success result
            return {
                "success": True,
                "agent_id": agent_id,
                "step_id": step_context["step_id"],
                "result": "Step executed successfully",
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Agent {agent_id} failed to execute step: {e}")
            return {
                "success": False,
                "agent_id": agent_id,
                "error": str(e),
                "timestamp": time.time()
            }

    async def _handle_step_outputs(self, step: WorkflowStep, execution: WorkflowExecution) -> None:
        """Handle step output variables"""

        if not step.output_variables:
            return

        step_result = execution.step_results.get(step.id, {})

        for var_name, result_key in step.output_variables.items():
            if result_key in step_result:
                value = step_result[result_key]
                execution.variables[var_name] = WorkflowVariable(
                    name=var_name,
                    value=value,
                    type=type(value).__name__,
                    description=f"Output from step {step.id}"
                )

    async def _drop_pheromone(self, signal_type: str, payload: Dict[str, Any], project_id: str) -> None:
        """Drop pheromone signal"""

        if self.pheromone_bus:
            try:
                # Use pheromone bus if available
                await self.pheromone_bus.drop_pheromone(
                    signal=signal_type,
                    payload=payload,
                    project_id=project_id
                )
            except Exception as e:
                logger.warning(f"Failed to drop pheromone: {e}")
        else:
            # Log pheromone for debugging
            logger.info(f"Pheromone: {signal_type} - {payload}")

    # Public API methods

    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get execution status by ID"""
        return self.active_executions.get(execution_id)

    def get_all_executions(self) -> List[WorkflowExecution]:
        """Get all active executions"""
        return list(self.active_executions.values())

    async def pause_execution(self, execution_id: str) -> bool:
        """Pause workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.RUNNING:
            execution.status = WorkflowStatus.PAUSED
            return True
        return False

    async def resume_execution(self, execution_id: str) -> bool:
        """Resume paused workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status == WorkflowStatus.PAUSED:
            execution.status = WorkflowStatus.RUNNING
            return True
        return False

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel workflow execution"""
        execution = self.active_executions.get(execution_id)
        if execution and execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.PAUSED]:
            execution.status = WorkflowStatus.CANCELLED
            execution.completed_at = time.time()
            execution.execution_time = execution.completed_at - execution.started_at
            return True
        return False

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return {
            **self.execution_stats,
            "active_executions": len(self.active_executions),
            "agent_registry_size": len(self.agent_registry)
        }

# Global workflow engine instance
_global_engine: Optional[WorkflowExecutionEngine] = None

def get_workflow_engine() -> WorkflowExecutionEngine:
    """Get the global workflow engine instance"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine()
    return _global_engine

async def initialize_workflow_engine(pheromone_bus=None) -> WorkflowExecutionEngine:
    """Initialize the global workflow engine"""
    global _global_engine
    if _global_engine is None:
        _global_engine = WorkflowExecutionEngine(pheromone_bus)
    return _global_engine

# Convenience functions for backward compatibility

# Legacy WorkflowEngine is defined later in the file to maintain compatibility

# Legacy functions for backward compatibility

def load_workflow_definitions(workflows_dir: str = "workflows") -> Dict[str, WorkflowDefinition]:
    """Load all workflow definitions from directory"""
    workflows = {}
    workflows_path = Path(workflows_dir)

    if workflows_path.exists():
        parser = WorkflowYAMLParser()

        for yaml_file in workflows_path.glob("*.yaml"):
            try:
                workflow = parser.parse_workflow_file(yaml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yaml_file}: {e}")

        for yml_file in workflows_path.glob("*.yml"):
            try:
                workflow = parser.parse_workflow_file(yml_file)
                workflows[workflow.id] = workflow
            except Exception as e:
                logger.warning(f"Failed to load workflow {yml_file}: {e}")

    return workflows

def get_workflow_for_project_type(project_type: str) -> str:
    """Get appropriate workflow ID for project type"""
    workflow_mapping = {
        "fullstack": "greenfield-fullstack",
        "frontend": "greenfield-fullstack",
        "backend": "greenfield-fullstack",
        "mobile": "greenfield-fullstack",
        "api": "greenfield-fullstack",
        "enhancement": "enhancement",
        "bugfix": "debugging",
        "testing": "testing"
    }

    return workflow_mapping.get(project_type, "greenfield-fullstack")

async def execute_bmad_workflow(
    workflow_type: str,
    project_context: Dict[str, Any],
    agent_team: Dict[str, Any],
    pheromone_bus: Dict[str, Any]
) -> WorkflowExecution:
    """Execute BMAD workflow with full context"""

    engine = get_workflow_engine()

    return await engine.start_workflow(
        workflow_id=workflow_type,
        project_id=project_context.get("project_id", "unknown"),
        agent_team=agent_team,
        pheromone_bus=pheromone_bus,
        context=project_context
    )

# Export main classes and functions
__all__ = [
    # Core classes
    'WorkflowDefinition',
    'WorkflowExecution',
    'WorkflowStep',
    'WorkflowVariable',
    'WorkflowYAMLParser',
    'WorkflowExecutionEngine',

    # Enums
    'WorkflowStatus',
    'StepStatus',
    'StepType',
    'ConditionOperator',
    'AgentSelectionStrategy',

    # Data classes
    'StepCondition',
    'AgentRequirement',
    'RetryConfig',
    'TimeoutConfig',

    # Exceptions
    'WorkflowValidationError',
    'WorkflowExecutionError',
    'WorkflowTimeoutError',
    'AgentAssignmentError',

    # Global functions
    'get_workflow_engine',
    'initialize_workflow_engine',
    'load_workflow_definitions',
    'get_workflow_for_project_type',
    'execute_bmad_workflow',

    # Legacy compatibility
    'WorkflowEngine',
    'WorkflowPhase',  # Legacy
    'PhaseStatus'     # Legacy
]

class WorkflowEngine:
    """BMAD Workflow Engine for orchestrating multi-phase project generation"""
    
    def __init__(self):
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.bmad_path = Path("BMAD-METHOD-main/BMAD-METHOD-main")
        self._load_bmad_workflows()
    
    def _load_bmad_workflows(self):
        """Load BMAD workflow definitions from YAML files"""
        try:
            workflows_dir = self.bmad_path / "bmad-core" / "workflows"
            if not workflows_dir.exists():
                logger.warning(f"BMAD workflows directory not found: {workflows_dir}")
                self._create_default_workflows()
                return
            
            for workflow_file in workflows_dir.glob("*.yml"):
                try:
                    with open(workflow_file, 'r', encoding='utf-8') as f:
                        workflow_data = yaml.safe_load(f)
                    
                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                    self.workflows[workflow_def.id] = workflow_def
                    logger.info(f"Loaded workflow: {workflow_def.name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load workflow {workflow_file}: {e}")
            
            # Load expansion pack workflows
            expansion_dir = self.bmad_path / "expansion-packs"
            if expansion_dir.exists():
                for pack_dir in expansion_dir.iterdir():
                    if pack_dir.is_dir():
                        pack_workflows = pack_dir / "workflows"
                        if pack_workflows.exists():
                            for workflow_file in pack_workflows.glob("*.yml"):
                                try:
                                    with open(workflow_file, 'r', encoding='utf-8') as f:
                                        workflow_data = yaml.safe_load(f)
                                    
                                    workflow_def = self._parse_workflow_yaml(workflow_data, workflow_file.stem)
                                    self.workflows[workflow_def.id] = workflow_def
                                    logger.info(f"Loaded expansion workflow: {workflow_def.name}")
                                    
                                except Exception as e:
                                    logger.error(f"Failed to load expansion workflow {workflow_file}: {e}")
            
            logger.info(f"Loaded {len(self.workflows)} BMAD workflows")
            
        except Exception as e:
            logger.error(f"Failed to load BMAD workflows: {e}")
            self._create_default_workflows()
    
    def _parse_workflow_yaml(self, data: Dict[str, Any], file_id: str) -> WorkflowDefinition:
        """Parse YAML workflow data into WorkflowDefinition"""
        
        workflow_info = data.get('workflow', {})
        
        # Parse phases from sequence
        phases = []
        sequence = workflow_info.get('sequence', [])
        
        for i, step in enumerate(sequence):
            if isinstance(step, dict):
                # Handle different step formats
                if 'agent' in step:
                    phase = WorkflowPhase(
                        id=f"phase_{i+1}",
                        name=step.get('name', f"Phase {i+1}"),
                        description=step.get('notes', ''),
                        agent=step['agent'],
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', ''),
                        condition=step.get('condition')
                    )
                elif 'step' in step:
                    # Handle step-based format (like game workflows)
                    phase = WorkflowPhase(
                        id=step.get('step', f"phase_{i+1}"),
                        name=step.get('step', f"Phase {i+1}").replace('_', ' ').title(),
                        description=step.get('notes', ''),
                        agent=step.get('agent', 'developer'),
                        creates=step.get('creates', []) if isinstance(step.get('creates'), list) else [step.get('creates', '')],
                        requires=step.get('requires', []) if isinstance(step.get('requires'), list) else [step.get('requires', '')] if step.get('requires') else [],
                        optional_steps=step.get('optional_steps', []),
                        duration_minutes=self._parse_duration(step.get('duration', '30 minutes')),
                        notes=step.get('notes', '')
                    )
                else:
                    continue
                
                phases.append(phase)
        
        # Calculate total duration
        total_duration = sum(phase.duration_minutes for phase in phases)
        
        return WorkflowDefinition(
            id=workflow_info.get('id', file_id),
            name=workflow_info.get('name', file_id.replace('-', ' ').title()),
            version=workflow_info.get('version', '1.0.0'),  # Add default version
            description=workflow_info.get('description', ''),
            type=workflow_info.get('type', 'greenfield'),
            project_types=workflow_info.get('project_types', []),
            phases=phases,
            total_duration_minutes=total_duration,
            flow_diagram=workflow_info.get('flow_diagram', ''),
            metadata=workflow_info
        )
    
    def _parse_duration(self, duration_str: str) -> int:
        """Parse duration string to minutes"""
        if isinstance(duration_str, int):
            return duration_str
        
        duration_str = str(duration_str).lower()
        
        if 'hour' in duration_str:
            try:
                hours = float(duration_str.split()[0])
                return int(hours * 60)
            except:
                return 60
        elif 'minute' in duration_str:
            try:
                minutes = float(duration_str.split()[0].split('-')[0])
                return int(minutes)
            except:
                return 30
        else:
            return 30
    
    def _create_default_workflows(self):
        """Create default workflows when BMAD files are not available"""
        
        # Greenfield Full Stack Workflow
        fullstack_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze requirements and create user stories", "analyst", ["project-brief.md"], [], [], 30),
            WorkflowPhase("product_planning", "Product Planning", "Create comprehensive PRD", "pm", ["prd.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["prd.md"], ["user_research_prompt"], 60),
            WorkflowPhase("architecture_design", "Architecture Design", "Design system architecture", "architect", ["fullstack-architecture.md"], ["prd.md", "front-end-spec.md"], ["technical_research_prompt"], 45),
            WorkflowPhase("development", "Development", "Implement the application", "developer", ["source_code"], ["fullstack-architecture.md"], [], 120),
            WorkflowPhase("testing", "Testing & QA", "Test and validate implementation", "qa", ["test_results"], ["source_code"], [], 45),
            WorkflowPhase("documentation", "Documentation", "Create final documentation", "analyst", ["documentation"], ["source_code"], [], 30)
        ]
        
        self.workflows["greenfield-fullstack"] = WorkflowDefinition(
            id="greenfield-fullstack",
            name="Greenfield Full Stack Development",
            description="Complete full-stack application development from concept to deployment",
            type="greenfield",
            project_types=["fullstack", "web-app"],
            phases=fullstack_phases,
            total_duration_minutes=sum(p.duration_minutes for p in fullstack_phases)
        )
        
        # Frontend-only workflow
        frontend_phases = [
            WorkflowPhase("requirements_analysis", "Requirements Analysis", "Analyze UI requirements", "analyst", ["project-brief.md"], [], [], 20),
            WorkflowPhase("ux_design", "UX Design", "Create UI/UX specifications", "ux-expert", ["front-end-spec.md"], ["project-brief.md"], [], 45),
            WorkflowPhase("frontend_development", "Frontend Development", "Implement frontend application", "frontend-developer", ["frontend_code"], ["front-end-spec.md"], [], 90),
            WorkflowPhase("testing", "Testing", "Test frontend functionality", "qa", ["test_results"], ["frontend_code"], [], 30)
        ]
        
        self.workflows["greenfield-frontend"] = WorkflowDefinition(
            id="greenfield-frontend",
            name="Greenfield Frontend Development",
            description="Frontend-only application development",
            type="greenfield",
            project_types=["frontend", "spa"],
            phases=frontend_phases,
            total_duration_minutes=sum(p.duration_minutes for p in frontend_phases)
        )
        
        logger.info("Created default workflows")
    
    async def start_workflow(self, workflow_id: str, project_id: str, agent_team: Dict[str, Any], 
                           pheromone_bus: Dict[str, Any], context: Dict[str, Any]) -> WorkflowExecution:
        """Start a new workflow execution"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            project_id=project_id,
            status=WorkflowStatus.IN_PROGRESS,
            start_time=datetime.now(),
            agent_team=agent_team,
            pheromone_bus=pheromone_bus
        )
        
        self.executions[project_id] = execution
        
        # Drop pheromone for workflow start
        await self._drop_pheromone("workflow_started", {
            "workflow_id": workflow_id,
            "project_id": project_id,
            "total_phases": len(self.workflows[workflow_id].phases)
        }, project_id)
        
        logger.info(f"Started workflow {workflow_id} for project {project_id}")
        
        # Start execution in background
        asyncio.create_task(self._execute_workflow(execution, context))
        
        return execution
    
    async def _execute_workflow(self, execution: WorkflowExecution, context: Dict[str, Any]):
        """Execute workflow phases sequentially"""
        
        workflow = self.workflows[execution.workflow_id]
        
        try:
            for i, phase in enumerate(workflow.phases):
                execution.current_phase_index = i
                execution.progress = i / len(workflow.phases)
                
                # Check if phase should be skipped based on condition
                if phase.condition and not self._evaluate_condition(phase.condition, execution.phase_results):
                    phase.status = PhaseStatus.SKIPPED
                    logger.info(f"Skipping phase {phase.name} due to condition: {phase.condition}")
                    continue
                
                # Execute phase
                phase.status = PhaseStatus.IN_PROGRESS
                phase.start_time = datetime.now()
                
                await self._drop_pheromone("phase_started", {
                    "phase_id": phase.id,
                    "phase_name": phase.name,
                    "agent": phase.agent,
                    "estimated_duration": phase.duration_minutes
                }, execution.project_id)
                
                try:
                    phase_result = await self._execute_phase(phase, execution, context)
                    execution.phase_results[phase.id] = phase_result
                    
                    phase.status = PhaseStatus.COMPLETED
                    phase.end_time = datetime.now()
                    phase.outputs = phase_result.get("outputs", [])
                    
                    await self._drop_pheromone("phase_completed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "success": phase_result.get("success", False),
                        "outputs": phase.outputs,
                        "duration_minutes": (phase.end_time - phase.start_time).total_seconds() / 60
                    }, execution.project_id)
                    
                except Exception as e:
                    phase.status = PhaseStatus.FAILED
                    phase.end_time = datetime.now()
                    execution.error_message = str(e)
                    
                    await self._drop_pheromone("phase_failed", {
                        "phase_id": phase.id,
                        "phase_name": phase.name,
                        "error": str(e)
                    }, execution.project_id)
                    
                    logger.error(f"Phase {phase.name} failed: {e}")
                    break
            
            # Complete workflow
            execution.status = WorkflowStatus.COMPLETED
            execution.end_time = datetime.now()
            execution.progress = 1.0
            
            await self._drop_pheromone("workflow_completed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "total_duration_minutes": (execution.end_time - execution.start_time).total_seconds() / 60,
                "phases_completed": sum(1 for phase in workflow.phases if phase.status == PhaseStatus.COMPLETED)
            }, execution.project_id)
            
            logger.info(f"Workflow {execution.workflow_id} completed for project {execution.project_id}")
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.end_time = datetime.now()
            execution.error_message = str(e)
            
            await self._drop_pheromone("workflow_failed", {
                "workflow_id": execution.workflow_id,
                "project_id": execution.project_id,
                "error": str(e)
            }, execution.project_id)
            
            logger.error(f"Workflow {execution.workflow_id} failed: {e}")
    
    async def _execute_phase(self, phase: WorkflowPhase, execution: WorkflowExecution, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow phase"""
        
        # Import agent execution here to avoid circular imports
        try:
            from .agent_executors import create_agent_executor
            
            # Create agent executor for this phase
            agent_executor = create_agent_executor(phase.agent)
            
            # Prepare phase context
            phase_context = {
                **context,
                "phase": phase.id,
                "phase_name": phase.name,
                "creates": phase.creates,
                "requires": phase.requires,
                "optional_steps": phase.optional_steps,
                "previous_results": execution.phase_results
            }
            
            # Execute the phase
            result = await agent_executor.execute(phase_context)
            
            return result
            
        except ImportError:
            # Fallback to basic execution
            logger.warning(f"Agent executors not available, using fallback execution for phase {phase.name}")
            
            await asyncio.sleep(1)  # Simulate work
            
            return {
                "success": True,
                "outputs": phase.creates,
                "message": f"Fallback execution of {phase.name} completed",
                "files_created": len(phase.creates)
            }
    
    def _evaluate_condition(self, condition: str, phase_results: Dict[str, Any]) -> bool:
        """Evaluate a phase condition"""
        # Simple condition evaluation - can be extended
        if condition == "architecture_suggests_prd_changes":
            arch_result = phase_results.get("architecture_design", {})
            return arch_result.get("suggests_prd_changes", False)
        
        return True  # Default to true for unknown conditions
    
    async def _drop_pheromone(self, signal: str, payload: Dict[str, Any], project_id: str):
        """Drop a pheromone signal"""
        try:
            from .pheromone_system import get_pheromone_system
            pheromone_system = get_pheromone_system()
            await pheromone_system.drop_pheromone(signal, payload, project_id)
        except Exception as e:
            logger.warning(f"Failed to drop pheromone: {e}")
    
    def get_workflow(self, workflow_id: str) -> Optional[WorkflowDefinition]:
        """Get workflow definition by ID"""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[WorkflowDefinition]:
        """List all available workflows"""
        return list(self.workflows.values())
    
    def get_execution(self, project_id: str) -> Optional[WorkflowExecution]:
        """Get workflow execution by project ID"""
        return self.executions.get(project_id)
    
    def get_workflows_for_project_type(self, project_type: str) -> List[WorkflowDefinition]:
        """Get workflows suitable for a project type"""
        return [w for w in self.workflows.values() if project_type in w.project_types or not w.project_types]

    def get_workflow_types(self) -> List[str]:
        """Get available workflow types"""
        return [
            "greenfield-fullstack",
            "enhancement",
            "debugging",
            "testing"
        ]

# Global workflow engine instance
_workflow_engine = None

def get_workflow_engine() -> WorkflowEngine:
    """Get the global workflow engine instance"""
    global _workflow_engine
    if _workflow_engine is None:
        _workflow_engine = WorkflowEngine()
    return _workflow_engine
